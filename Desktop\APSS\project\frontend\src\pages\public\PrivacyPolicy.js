import React from "react";
import { Container, <PERSON>, Col, Card } from "react-bootstrap";

const PrivacyPolicy = () => {
  return (
    <Container className="py-5 mt-5">
      <Row className="justify-content-center">
        <Col md={10}>
          <Card className="shadow border-0">
            <Card.Body className="p-5">
              <h1 className="text-center mb-4">Privacy Policy</h1>
              
              <div className="mb-4">
                <h3>Information We Collect</h3>
                <p>
                  We collect information you provide directly to us, such as when you create an account, 
                  book an appointment, or contact us for support. This may include your name, email address, 
                  phone number, medical information, and other personal details necessary for providing 
                  healthcare services.
                </p>
              </div>

              <div className="mb-4">
                <h3>How We Use Your Information</h3>
                <p>
                  We use the information we collect to:
                </p>
                <ul>
                  <li>Provide, maintain, and improve our services</li>
                  <li>Process appointments and communicate with you</li>
                  <li>Send you technical notices and support messages</li>
                  <li>Comply with legal obligations</li>
                </ul>
              </div>

              <div className="mb-4">
                <h3>Information Sharing</h3>
                <p>
                  We do not sell, trade, or otherwise transfer your personal information to third parties 
                  without your consent, except as described in this policy. We may share your information 
                  with healthcare providers involved in your care and as required by law.
                </p>
              </div>

              <div className="mb-4">
                <h3>Data Security</h3>
                <p>
                  We implement appropriate security measures to protect your personal information against 
                  unauthorized access, alteration, disclosure, or destruction. However, no method of 
                  transmission over the internet is 100% secure.
                </p>
              </div>

              <div className="mb-4">
                <h3>Your Rights</h3>
                <p>
                  You have the right to access, update, or delete your personal information. You may also 
                  opt out of certain communications from us. To exercise these rights, please contact us 
                  using the information provided below.
                </p>
              </div>

              <div className="mb-4">
                <h3>Contact Us</h3>
                <p>
                  If you have any questions about this Privacy Policy, please contact us at:
                </p>
                <ul>
                  <li>Email: <EMAIL></li>
                  <li>Phone: (*************</li>
                  <li>Address: 123 Healthcare Ave, Medical City, MC 12345</li>
                </ul>
              </div>

              <div className="text-muted">
                <small>Last updated: {new Date().toLocaleDateString()}</small>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PrivacyPolicy;
