import React from "react";
import { Link, useLocation } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faHome, 
  faUser, 
  faCalendarAlt, 
  faCreditCard, 
  faFileMedical, 
  faComments, 
  faBell, 
  faStar,
  faSignOutAlt
} from "@fortawesome/free-solid-svg-icons";
import "./Sidebar.css";

const PatientSidebar = ({ onLogout }) => {
  const location = useLocation();
  const user = JSON.parse(localStorage.getItem("user"));

  const menuItems = [
    {
      path: "/patient/dashboard",
      icon: faHome,
      label: "Dashboard"
    },
    {
      path: "/patient/profile",
      icon: faUser,
      label: "Profile"
    },
    {
      path: "/patient/book-appointment",
      icon: faCalendarAlt,
      label: "Book Appointment"
    },
    {
      path: "/patient/appointments",
      icon: faCalendarAlt,
      label: "My Appointments"
    },
    {
      path: "/patient/payments",
      icon: faCreditCard,
      label: "Payments"
    },
    {
      path: "/patient/records",
      icon: faFileMedical,
      label: "Medical Records"
    },
    {
      path: "/patient/messages",
      icon: faComments,
      label: "Messages"
    },
    {
      path: "/patient/notifications",
      icon: faBell,
      label: "Notifications"
    },
    {
      path: "/patient/feedback",
      icon: faStar,
      label: "Feedback"
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Patient Portal</h3>
        <p className="user-info">Welcome, {user?.name || 'Patient'}</p>
      </div>
      
      <nav className="sidebar-nav">
        <ul>
          {menuItems.map((item) => (
            <li key={item.path}>
              <Link 
                to={item.path} 
                className={location.pathname === item.path ? 'active' : ''}
              >
                <FontAwesomeIcon icon={item.icon} className="me-2" />
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className="sidebar-footer">
        <button onClick={onLogout} className="logout-btn">
          <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default PatientSidebar; 