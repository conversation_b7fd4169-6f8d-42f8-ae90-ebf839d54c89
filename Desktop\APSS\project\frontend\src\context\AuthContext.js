import { createContext, useState, useEffect, useContext } from "react";
import React from 'react';
import { USER_ROLES } from "../assets/utils/constants";

const AuthContext = createContext();

// Custom hook to access auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user data exists in localStorage
    const storedUser = JSON.parse(localStorage.getItem("user"));
    if (storedUser && storedUser.token) {
      // Check if token is expired (if you have expiration in token)
      // For now, we'll just set the user
      setUser(storedUser);
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const login = (userData) => {
    // Store the entire response including token
    const userToStore = {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      role: userData.role,
      token: userData.token // JWT token
    };
    
    setUser(userToStore);
    setIsAuthenticated(true);
    localStorage.setItem("user", JSON.stringify(userToStore));
    
    return userToStore.role; // Return role for redirection
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem("user");
  };

  const getHomeRoute = () => {
    if (!user) return "/login";
    
    const role = user.role.toLowerCase();
    if (role === USER_ROLES.PATIENT) {
      return "/patient/dashboard";
    } else if (role === USER_ROLES.DOCTOR) {
      return "/doctor/dashboard";
    } else if (role === USER_ROLES.ADMIN) {
      return "/admin/dashboard";
    }
    return "/";
  };

  // Show loading indicator while checking authentication
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "100vh" }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  const value = {
    user,
    isAuthenticated,
    login,
    logout,
    getHomeRoute
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
