package Backend.project.repositories;

import Backend.project.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);
    List<User> findByRole(String role);
    
    // Add missing methods
    long countByRole(String role);
    boolean existsByEmail(String email);

    long count();
}
