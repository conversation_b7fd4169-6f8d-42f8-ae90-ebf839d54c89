package Backend.project.config;

import Backend.project.model.User;
import Backend.project.model.Role;
import Backend.project.model.Specialty;
import Backend.project.repositories.UserRepository;
import Backend.project.repositories.SpecialtyRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final SpecialtyRepository specialtyRepository;

    public DataInitializer(UserRepository userRepository, PasswordEncoder passwordEncoder, SpecialtyRepository specialtyRepository) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.specialtyRepository = specialtyRepository;
    }

    @Override
    public void run(String... args) {
        // Create default admin if not exists
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User admin = new User();
            admin.setName("Admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setRole(String.valueOf(Role.ADMIN));
            userRepository.save(admin);
            System.out.println("✅ Default admin created: <EMAIL> / admin123");
        } else {
            System.out.println("ℹ️ Admin already exists.");
        }
        
        // Create default specialties if none exist
        if (specialtyRepository.count() == 0) {
            List<Specialty> defaultSpecialties = Arrays.asList(
                createSpecialty("Cardiology", "Deals with disorders of the heart and blood vessels"),
                createSpecialty("Dermatology", "Focuses on diseases of the skin, hair, and nails"),
                createSpecialty("Neurology", "Addresses disorders of the nervous system"),
                createSpecialty("Orthopedics", "Concerned with the musculoskeletal system"),
                createSpecialty("Pediatrics", "Manages medical care of infants, children, and adolescents"),
                createSpecialty("Psychiatry", "Deals with the diagnosis and treatment of mental disorders"),
                createSpecialty("Ophthalmology", "Focuses on eye and vision care"),
                createSpecialty("Gynecology", "Deals with health of the female reproductive system")
            );
            
            specialtyRepository.saveAll(defaultSpecialties);
            System.out.println("✅ Default specialties created: " + defaultSpecialties.size());
        } else {
            System.out.println("ℹ️ Specialties already exist: " + specialtyRepository.count());
        }
    }
    
    private Specialty createSpecialty(String name, String description) {
        Specialty specialty = new Specialty();
        specialty.setName(name);
        specialty.setDescription(description);
        return specialty;
    }
}
