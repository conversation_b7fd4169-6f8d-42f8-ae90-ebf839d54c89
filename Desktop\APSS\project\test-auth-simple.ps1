# PowerShell script to test authentication endpoints

$baseUrl = "http://localhost:8080/api/auth"

# Test registration
$registerData = @{
    name = "Test User"
    email = "<EMAIL>"
    password = "password123"
    role = "PATIENT"
} | ConvertTo-Json

Write-Host "Testing registration..." -ForegroundColor Cyan
try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/register" -Method Post -Body $registerData -ContentType "application/json" -ErrorAction Stop
    Write-Host "Registration successful!" -ForegroundColor Green
    Write-Host "Token: $($registerResponse.token)" -ForegroundColor Green
    Write-Host "User ID: $($registerResponse.id)" -ForegroundColor Green
    Write-Host "Role: $($registerResponse.role)" -ForegroundColor Green
} catch {
    Write-Host "Registration failed with status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Error message: $($_.Exception.Message)" -ForegroundColor Red
}
