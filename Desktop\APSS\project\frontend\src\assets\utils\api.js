import axios from "axios";
import { API_BASE_URL } from "./constants";

// Create a single Axios instance
const api = axios.create({
  baseURL: API_BASE_URL || "http://localhost:8080",
  headers: { "Content-Type": "application/json" },
  timeout: 10000,
  withCredentials: true,
});

// Add a request interceptor to include JWT token
api.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (user && user.token) {
      config.headers["Authorization"] = `Bearer ${user.token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Handle token expiration globally
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      localStorage.removeItem("user");
      window.location.href = "/login?expired=true";
    }
    return Promise.reject(error);
  }
);

// Reusable wrapper for API requests
const handleApiRequest = async (requestFn) => {
  try {
    const response = await requestFn();
    return response;
  } catch (error) {
    console.error("API request failed:", error.message);
    throw error;
  }
};

// === AUTH ===
export const loginUser = async (credentials) => {
  try {
    const response = await api.post("/api/auth/login", credentials);
    return response.data;
  } catch (error) {
    console.error("Login failed:", error);
    throw error;
  }
};

export const register = async (userData) => {
  try {
    const response = await api.post("/api/auth/register", userData);
    return response.data;
  } catch (error) {
    console.error("Registration failed:", error);
    throw error;
  }
};

export const sendResetEmail = async (email) => {
  const res = await api.post("/api/auth/forgot-password", { email });
  return res.data;
};

export const resetPassword = async ({ token, password }) => {
  const res = await api.post("/api/auth/reset-password", { token, password });
  return res.data;
};

// === CONTACT FORM ===
export const submitContactForm = async (contactData) => {
  try {
    const response = await api.post("/api/public/contact", contactData);
    return response.data;
  } catch (error) {
    console.error("Contact form submission failed:", error);
    throw error;
  }
};

// === APPOINTMENTS ===
export const getAppointments = () =>
  handleApiRequest(() => api.get("/api/appointments"), "appointments");

export const getPatientAppointments = (patientId) =>
  handleApiRequest(() => api.get(`/api/appointments/patient/${patientId}`), "appointments");

export const getDoctorAppointments = (doctorId) =>
  handleApiRequest(() => api.get(`/api/appointments/doctor/${doctorId}`), "appointments");

export const getDoctorUpcomingAppointments = (doctorId) =>
  handleApiRequest(() => api.get(`/api/appointments/doctor/${doctorId}/upcoming`), "appointments");

export const getPatientUpcomingAppointments = (patientId) =>
  // Modified to use the existing endpoint and filter upcoming appointments on the frontend
  handleApiRequest(() => api.get(`/api/appointments/patient/${patientId}`), "appointments");

export const bookAppointment = (appointmentData) =>
  handleApiRequest(() => api.post("/api/appointments", appointmentData));

export const confirmAppointment = (appointmentId) =>
  handleApiRequest(() => api.put(`/api/appointments/${appointmentId}/confirm`));

export const cancelAppointment = (appointmentId) =>
  handleApiRequest(() => api.put(`/api/appointments/${appointmentId}/cancel`));

// === DOCTORS ===
export const getAllDoctors = () =>
  handleApiRequest(() => api.get("/api/doctors"), "doctors");

export const getDoctorById = (id) =>
  handleApiRequest(() => api.get(`/api/doctors/${id}`));

export const getDoctorAvailability = (doctorId, date) =>
  handleApiRequest(() => api.get(`/api/doctors/${doctorId}/availability?date=${date}`));

export const getDoctorStats = (doctorId) =>
  handleApiRequest(() => api.get(`/api/dashboard/doctor/${doctorId}/stats`));

// === NOTIFICATIONS ===
export const getPatientNotifications = (patientId) =>
  handleApiRequest(() => api.get(`/api/notifications/patient/${patientId}`), "notifications");

export const markNotificationAsRead = (notificationId) =>
  handleApiRequest(() => api.put(`/api/notifications/${notificationId}/read`));

// === DASHBOARD ===
export const getAdminDashboardStats = async () => {
  try {
    // Add a cache-busting parameter to ensure we get fresh data
    const timestamp = new Date().getTime();
    const response = await api.get(`/api/admin/stats?_=${timestamp}`);
    console.log('Admin dashboard stats response:', response);
    return response;
  } catch (error) {
    console.error('Failed to fetch admin dashboard stats:', error);
    throw error;
  }
};

export const getPatientDashboardStats = (patientId) =>
  // Updated to match the actual backend endpoint
  handleApiRequest(() => api.get(`/api/dashboard/patient/${patientId}/stats`), 'patientStats');

// === ADMIN FUNCTIONS ===
export const getAllUsers = () =>
  handleApiRequest(() => api.get('/api/admin/users'), 'users');

export const getAllPatients = () =>
  handleApiRequest(() => api.get('/api/admin/patients'), 'patients');

export const getAllAppointments = () =>
  handleApiRequest(() => api.get('/api/admin/appointments'), 'appointments');

export const getAllSpecialties = async () => {
  // Try multiple endpoints with fallback mechanism
  try {
    // First try the public endpoint
    try {
      const response = await api.get('/api/public/specialties');
      console.log("Successfully fetched specialties from public endpoint");
      return response;
    } catch (publicError) {
      console.log("Could not fetch from public endpoint, trying alternative endpoint...");
      
      // If public endpoint fails, try the regular endpoint
      try {
        const response = await api.get('/api/specialties');
        console.log("Successfully fetched specialties from regular endpoint");
        return response;
      } catch (regularError) {
        // If that fails too, try the admin endpoint as last resort
        console.log("Regular endpoint failed too, trying admin endpoint...");
        const response = await api.get('/api/admin/specialties');
        console.log("Successfully fetched specialties from admin endpoint");
        return response;
      }
    }
  } catch (error) {
    console.error("All specialty endpoints failed:", error);
    throw error;
  }
};

export const createUser = (userData) =>
  handleApiRequest(() => api.post('/api/admin/users', userData));

export const updateUser = (userId, userData) =>
  handleApiRequest(() => api.put(`/api/admin/users/${userId}`, userData));

export const deleteUser = (userId) =>
  handleApiRequest(() => api.delete(`/api/admin/users/${userId}`));

export const createSpecialty = (specialtyData) =>
  handleApiRequest(() => api.post('/api/admin/specialties', specialtyData));

export const updateSpecialty = (specialtyId, specialtyData) =>
  handleApiRequest(() => api.put(`/api/admin/specialties/${specialtyId}`, specialtyData));

export const deleteSpecialty = (specialtyId) =>
  handleApiRequest(() => api.delete(`/api/admin/specialties/${specialtyId}`));

// === NOTIFICATIONS ADDITIONAL FUNCTIONS ===
export const deleteNotification = (notificationId) =>
  handleApiRequest(() => api.delete(`/api/notifications/${notificationId}`));

export const markAllNotificationsAsRead = (userId) =>
  handleApiRequest(() => api.put(`/api/notifications/user/${userId}/read-all`));

// === PAYMENTS ===
export const getPatientPayments = (patientId) =>
  handleApiRequest(() => api.get(`/api/payments/patient/${patientId}`), "payments");

export const createPayment = (paymentData) =>
  handleApiRequest(() => api.post(`/api/payments`, paymentData), "payments");

export const processPayment = (paymentId, provider) =>
  handleApiRequest(() => api.post(`/api/payments/${paymentId}/process`, { provider }), "payments");

export const getPaymentMethods = () =>
  handleApiRequest(() => api.get(`/api/payments/methods`), "payments");

export const getPaymentProviders = () =>
  handleApiRequest(() => api.get(`/api/payments/providers`), "payments");

export default api;
