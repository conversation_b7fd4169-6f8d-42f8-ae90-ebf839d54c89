// src/components/common/Navbar.js
import React from 'react';
import { Nav, Navbar, Container } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const CustomNavbar = () => {
  const { isAuthenticated, user, logout } = useAuth();

  return (
    <Navbar expand="lg" fixed="top" bg="light" className="shadow-sm">
      <Container>
        <Navbar.Brand as={Link} to="/" className="fw-bold text-primary">MediAppoint</Navbar.Brand>
        <Navbar.Toggle aria-controls="navbarScroll" />
        <Navbar.Collapse id="navbarScroll">
          <Nav className="ms-auto my-2 my-lg-0" navbarScroll>
            <Nav.Link as={Link} to="/">Home</Nav.Link>
            <Nav.Link href="#about">About</Nav.Link>
            <Nav.Link href="#services">Services</Nav.Link>
            <Nav.Link as={Link} to="/doctors">Doctors</Nav.Link>
            <Nav.Link href="#faq">FAQ</Nav.Link>
            <Nav.Link href="#contact">Contact</Nav.Link>
            
            {isAuthenticated ? (
              <>
                <Nav.Link as={Link} to={`/${user.role.toLowerCase()}/dashboard`} className="btn btn-outline-primary ms-2">
                  Dashboard
                </Nav.Link>
                <Nav.Link onClick={logout} className="btn btn-outline-danger ms-2">
                  Logout
                </Nav.Link>
              </>
            ) : (
              <>
                <Nav.Link as={Link} to="/login" className="btn btn-outline-primary ms-2">Login</Nav.Link>
                <Nav.Link as={Link} to="/register" className="btn btn-primary text-white ms-2">Sign Up</Nav.Link>
              </>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default CustomNavbar;
