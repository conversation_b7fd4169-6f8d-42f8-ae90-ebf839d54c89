import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Spin<PERSON>, <PERSON>, <PERSON>, Col } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { USER_ROLES, API_BASE_URL } from "../../assets/utils/constants";
import { register, getAllSpecialties } from "../../assets/utils/api";
import "../../styles/form.css";

const Register = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: USER_ROLES.PATIENT.toUpperCase(),
    specialtyId: "",
    licenseNumber: ""
  });
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const [specialties, setSpecialties] = useState([]);
  const [loadingSpecialties, setLoadingSpecialties] = useState(false);
  const navigate = useNavigate();
  
  // Fetch specialties when role is doctor
  useEffect(() => {
    if (formData.role === USER_ROLES.DOCTOR.toUpperCase()) {
      fetchSpecialties();
    }
  }, [formData.role]);
  
  const fetchSpecialties = async () => {
    setLoadingSpecialties(true);
    try {
      // First try the public endpoint
      let specialtiesData = [];
      try {
        const response = await fetch(`${API_BASE_URL}/api/public/specialties`);
        if (response.ok) {
          specialtiesData = await response.json();
          console.log('Specialties fetched from public endpoint:', specialtiesData);
        } else {
          console.log('Public specialties endpoint failed, trying alternative endpoint');
          throw new Error('Public endpoint failed');
        }
      } catch (publicErr) {
        // If public endpoint fails, try the specialties endpoint
        try {
          const altResponse = await fetch(`${API_BASE_URL}/api/specialties`);
          if (altResponse.ok) {
            specialtiesData = await altResponse.json();
            console.log('Specialties fetched from alternative endpoint:', specialtiesData);
          } else {
            throw new Error(`HTTP error! Status: ${altResponse.status}`);
          }
        } catch (altErr) {
          console.error('All specialty endpoints failed:', altErr);
          throw altErr; // Re-throw to be caught by the outer catch
        }
      }
      
      if (Array.isArray(specialtiesData) && specialtiesData.length > 0) {
        setSpecialties(specialtiesData);
      } else {
        console.warn('No specialties found or invalid data format');
        setSpecialties([]);
      }
    } catch (err) {
      console.error("Failed to fetch specialties:", err);
      setSpecialties([]);
    } finally {
      setLoadingSpecialties(false);
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setLoading(false);
      return;
    }

    try {
      // Prepare registration data based on role
      const registrationData = {
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role
      };
      
      // Add doctor-specific fields if role is doctor
      if (formData.role === USER_ROLES.DOCTOR.toUpperCase()) {
        registrationData.specialtyId = formData.specialtyId || null;
        registrationData.licenseNumber = formData.licenseNumber || null;
      }
      
      const response = await register(registrationData);

      setSuccess("Registration successful! You can now log in.");
      // Clear form
      setFormData({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: USER_ROLES.PATIENT.toUpperCase(),
        specialtyId: "",
        licenseNumber: ""
      });

      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      console.error("Registration error:", err);
      if (err.response && err.response.data) {
        setError(err.response.data);
      } else {
        setError("Registration failed. Please try again later.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col md={6} lg={5}>
          <Card className="shadow-sm border-0">
            <Card.Body className="p-4">
              <div className="text-center mb-4">
                <h2 className="fw-bold">Create an Account</h2>
                <p className="text-muted">Join our healthcare platform</p>
              </div>

              {error && <Alert variant="danger">{error}</Alert>}
              {success && <Alert variant="success">{success}</Alert>}

              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>Full Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Email Address</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Password</Form.Label>
                  <Form.Control
                    type="password"
                    name="password"
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    minLength="6"
                  />
                  <Form.Text className="text-muted">
                    Password must be at least 6 characters long
                  </Form.Text>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Confirm Password</Form.Label>
                  <Form.Control
                    type="password"
                    name="confirmPassword"
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Register as</Form.Label>
                  <Form.Select
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    required
                  >
                    <option value={USER_ROLES.PATIENT.toUpperCase()}>Patient</option>
                    <option value={USER_ROLES.DOCTOR.toUpperCase()}>Doctor</option>
                  </Form.Select>
                </Form.Group>
                
                {/* Doctor-specific fields */}
                {formData.role === USER_ROLES.DOCTOR.toUpperCase() && (
                  <>
                    <Form.Group className="mb-3">
                      <Form.Label>Medical License Number</Form.Label>
                      <Form.Control
                        type="text"
                        name="licenseNumber"
                        placeholder="Enter your license number"
                        value={formData.licenseNumber}
                        onChange={handleChange}
                      />
                    </Form.Group>
                    
                    <Form.Group className="mb-4">
                      <Form.Label>Specialty</Form.Label>
                      <Form.Select
                        name="specialtyId"
                        value={formData.specialtyId}
                        onChange={handleChange}
                        disabled={loadingSpecialties}
                      >
                        <option value="">Select your specialty</option>
                        {Array.isArray(specialties) && specialties.length > 0 ? (
                          specialties.map(specialty => (
                            <option 
                              key={specialty.id || specialty.name} 
                              value={specialty.id || specialty.name}
                            >
                              {specialty.name}
                            </option>
                          ))
                        ) : (
                          <option disabled>No specialties available</option>
                        )}
                      </Form.Select>
                      {loadingSpecialties && (
                        <div className="text-center mt-2">
                          <Spinner animation="border" size="sm" />
                          <small className="ms-2">Loading specialties...</small>
                        </div>
                      )}
                      {!loadingSpecialties && specialties.length === 0 && (
                        <div className="text-danger mt-1">
                          <small>Could not load specialties. Please try again later.</small>
                        </div>
                      )}
                    </Form.Group>
                  </>
                )}

                <Button
                  variant="primary"
                  type="submit"
                  className="w-100 py-2 mb-3"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" className="me-2" />
                      Creating Account...
                    </>
                  ) : (
                    "Create Account"
                  )}
                </Button>
              </Form>
            </Card.Body>
            <Card.Footer className="bg-white text-center border-0 py-3">
              Already have an account?{" "}
              <Link to="/login" className="text-decoration-none fw-bold">
                Sign In
              </Link>
            </Card.Footer>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Register;
