import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
//import "../Styles/SpecialtyList.css";

const SpecialtyList = () => {
  const [specialties, setSpecialties] = useState([]);

  useEffect(() => {

    setSpecialties([
      { id: 1, name: "Cardiology", description: "Heart and cardiovascular disease care." },
      { id: 2, name: "Dermatology", description: "Treatment for skin diseases." },
      { id: 3, name: "Pediatrics", description: "Medical care for children." },
      { id: 4, name: "Neurology", description: "Treatment of brain and nervous system diseases." }
    ]);
  }, []);

  return (
    <div className="specialty-container">
      <h2>Our Medical Specialties</h2>
      <div className="specialty-list">
        {specialties.map((specialty) => (
          <div key={specialty.id} className="specialty-card">
            <h3>{specialty.name}</h3>
            <p>{specialty.description}</p>
            <Link to={`/appointments?specialty=${specialty.id}`} className="btn-primary">
              Take an appointment
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SpecialtyList;
