package Backend.project.repositories;

import Backend.project.model.Appointment;
import Backend.project.model.AppointmentStatus;
import Backend.project.model.Doctor;
import Backend.project.model.Patient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    List<Appointment> findByPatient(Patient patient);
    List<Appointment> findByDoctor(Doctor doctor);
    List<Appointment> findByDoctorIdAndAppointmentDate(Long doctorId, LocalDate appointmentDate);
    
    // Methods for checking conflicting appointments
    List<Appointment> findByDoctorIdAndAppointmentDateBetween(Long doctorId, LocalDateTime start, LocalDateTime end);
    List<Appointment> findByDoctorIdAndAppointmentDateBetweenAndIdNot(Long doctorId, LocalDateTime start, LocalDateTime end, Long appointmentId);
    
    // Methods for upcoming appointments
    List<Appointment> findByPatientIdAndAppointmentDateAfterOrderByAppointmentDate(Long patientId, LocalDateTime dateTime);
    List<Appointment> findByDoctorIdAndAppointmentDateAfterOrderByAppointmentDate(Long doctorId, LocalDateTime dateTime);
    
    // Method for finding appointments needing reminders
    List<Appointment> findByAppointmentDateBetweenAndReminderSentFalseAndStatusNot(
            LocalDateTime start, LocalDateTime end, AppointmentStatus status);
            
    // Add missing method
    long countByStatus(String status);

    long count();
}
