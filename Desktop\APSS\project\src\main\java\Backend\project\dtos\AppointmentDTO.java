package Backend.project.dtos;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class AppointmentDTO {
    private Long id;
    private LocalDate appointmentDate;
    private String time;
    private String patientName;
    private String reason;

    public AppointmentDTO(Long id, LocalDate date, String time, String patientName, String reason) {
        this.id = id;
        this.appointmentDate = date;
        this.time = time;
        this.patientName = patientName;
        this.reason = reason;
    }

    public AppointmentDTO(Long id, LocalDateTime appointmentDate, Object time, String name, Object reason) {
    }
    // Getters and setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(LocalDate appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

