package Backend.project.dtos;

public class ForgotPasswordRequest {
    private String email;

    // Default constructor
    public ForgotPasswordRequest() {
    }

    // Parameterized constructor
    public ForgotPasswordRequest(String email) {
        this.email = email;
    }

    // Getters and Setters
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
