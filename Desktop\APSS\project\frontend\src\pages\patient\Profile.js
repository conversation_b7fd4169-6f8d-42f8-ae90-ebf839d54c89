import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser, faEnvelope, faPhone, faIdCard, faCalendarAlt, faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import "../../styles/form.css";

const Profile = () => {
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    dateOfBirth: "",
    gender: "",
    bloodGroup: "",
    allergies: "",
    medicalHistory: ""
  });
  
  const [loading, setLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Fetch user data on component mount
  useEffect(() => {
    const storedUser = JSON.parse(localStorage.getItem("user"));
    if (storedUser) {
      setUser(storedUser);
      setLoading(true);
      
      // Get the user ID from the stored user data
      const userId = storedUser.id;
      
      if (!userId) {
        console.error("No user ID found in stored user data");
        setError("User information is incomplete. Please log out and log in again.");
        setLoading(false);
        return;
      }
      
      console.log(`Fetching patient data for user ID: ${userId}`);
      
      // Fetch user profile data from API with authorization header
      const token = storedUser.token;
      axios.get(`${API_BASE_URL}/api/patients/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        console.log("Patient data received:", response.data);
        const patientData = response.data;
        
        if (!patientData) {
          throw new Error("No patient data returned from server");
        }
        
        // Initialize form with patient data
        setFormData({
          name: patientData.name || storedUser.name || "",
          email: patientData.email || storedUser.email || "",
          phone: patientData.phone || "",
          address: patientData.address || "",
          dateOfBirth: patientData.dateOfBirth || "",
          gender: patientData.gender || "",
          bloodGroup: patientData.bloodGroup || "",
          allergies: patientData.allergies || "",
          medicalHistory: patientData.medicalHistory || ""
        });
      })
      .catch(err => {
        console.error("Error fetching patient data:", err);
        setError("Failed to load your profile data. Please try again later.");
        
        // Initialize with data from localStorage as fallback
        setFormData({
          name: storedUser.name || "",
          email: storedUser.email || "",
          phone: "",
          address: "",
          dateOfBirth: "",
          gender: "",
          bloodGroup: "",
          allergies: "",
          medicalHistory: ""
        });
      })
      .finally(() => {
        setLoading(false);
      });
    }
  }, []);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setUpdateLoading(true);
    setError("");
    setSuccess("");
    
    if (!user) {
      setError("You must be logged in to update your profile.");
      setUpdateLoading(false);
      return;
    }
    
    // Get the user ID and token from the stored user data
    const userId = user.id;
    const token = user.token;
    
    if (!userId) {
      setError("User information is incomplete. Please log out and log in again.");
      setUpdateLoading(false);
      return;
    }
    
    console.log(`Updating profile for user ID: ${userId}`);
    console.log('Profile data being sent:', formData);
    
    // Update user profile via API with authorization header
    axios.put(`${API_BASE_URL}/api/patients/${userId}`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log("Profile updated successfully:", response.data);
      setSuccess("Your profile has been updated successfully!");
      
      // Update user in localStorage if name or email changed
      if (formData.name !== user.name || formData.email !== user.email) {
        const updatedUser = {
          ...user,
          name: formData.name,
          email: formData.email
        };
        localStorage.setItem("user", JSON.stringify(updatedUser));
        setUser(updatedUser);
      }
    })
    .catch(err => {
      console.error("Error updating profile:", err);
      if (err.response) {
        console.error("Response error data:", err.response.data);
        console.error("Response error status:", err.response.status);
        setError(`Failed to update your profile: ${err.response.data.message || err.response.data || 'Server error'}`); 
      } else if (err.request) {
        console.error("Request error:", err.request);
        setError("Failed to connect to the server. Please check your internet connection.");
      } else {
        setError("Failed to update your profile. Please try again later.");
      }
    })
    .finally(() => {
      setUpdateLoading(false);
    });
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-3">Loading your profile...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col md={8}>
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h4 className="mb-0">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Edit Profile
              </h4>
            </Card.Header>
            <Card.Body>
              {error && <Alert variant="danger">{error}</Alert>}
              {success && <Alert variant="success">{success}</Alert>}
              
              <Form onSubmit={handleSubmit}>
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        Full Name
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                        Email Address
                      </Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faPhone} className="me-2" />
                        Phone Number
                      </Form.Label>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                        Date of Birth
                      </Form.Label>
                      <Form.Control
                        type="date"
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Gender</Form.Label>
                      <Form.Select
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Blood Group</Form.Label>
                      <Form.Select
                        name="bloodGroup"
                        value={formData.bloodGroup}
                        onChange={handleChange}
                      >
                        <option value="">Select Blood Group</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                
                <Form.Group className="mb-3">
                  <Form.Label>
                    <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" />
                    Address
                  </Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                  />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Allergies (if any)</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="allergies"
                    value={formData.allergies}
                    onChange={handleChange}
                    placeholder="List any allergies you have"
                  />
                </Form.Group>
                
                <Form.Group className="mb-4">
                  <Form.Label>Medical History</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="medicalHistory"
                    value={formData.medicalHistory}
                    onChange={handleChange}
                    placeholder="Any important medical history to share with your doctors"
                  />
                </Form.Group>
                
                <div className="d-grid">
                  <Button 
                    variant="primary" 
                    type="submit"
                    disabled={updateLoading}
                    className="py-2"
                  >
                    {updateLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Updating Profile...
                      </>
                    ) : (
                      "Save Changes"
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Profile;
