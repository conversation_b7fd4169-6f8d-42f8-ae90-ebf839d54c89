import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, ListGroup, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBell, faCheck, faTrash } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import { 
  getPatientNotifications, 
  markNotificationAsRead, 
  deleteNotification,
  markAllNotificationsAsRead 
} from "../../assets/utils/api";
import { useAuth } from "../../context/AuthContext";

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  // We use setError but display errors in console for now
  const [, setError] = useState("");
  const { user } = useAuth();

  useEffect(() => {
    fetchNotifications();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await getPatientNotifications(user.id);
      setNotifications(response.data);
    } catch (err) {
      setError("Failed to load notifications");
      console.error("Error fetching notifications:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await markNotificationAsRead(notificationId);
      setNotifications(notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      ));
    } catch (err) {
      console.error("Error marking notification as read:", err);
    }
  };

  const handleDelete = async (notificationId) => {
    if (window.confirm("Are you sure you want to delete this notification?")) {
      try {
        await deleteNotification(notificationId);
        setNotifications(notifications.filter(n => n.id !== notificationId));
      } catch (err) {
        console.error("Error deleting notification:", err);
      }
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead(user.id);
      setNotifications(notifications.map(notification => ({
        ...notification,
        read: true
      })));
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" />
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center bg-white py-3">
          <h4 className="mb-0">
            <FontAwesomeIcon icon={faBell} className="me-2" />
            Notifications
            {notifications.filter(n => !n.read).length > 0 && (
              <Badge bg="danger" className="ms-2">
                {notifications.filter(n => !n.read).length}
              </Badge>
            )}
          </h4>
          {notifications.some(n => !n.read) && (
            <Button
              variant="outline-primary"
              size="sm"
              onClick={handleMarkAllAsRead}
            >
              <FontAwesomeIcon icon={faCheck} className="me-2" />
              Mark All as Read
            </Button>
          )}
        </Card.Header>
        <ListGroup variant="flush">
          {notifications.length > 0 ? (
            notifications.map(notification => (
              <ListGroup.Item
                key={notification.id}
                className={`py-3 ${!notification.read ? 'bg-light' : ''}`}
              >
                <div className="d-flex justify-content-between align-items-start">
                  <div className="me-3">
                    <p className="mb-1">{notification.message}</p>
                    <small className="text-muted">
                      {moment(notification.dateTimeSent).fromNow()}
                    </small>
                  </div>
                  <div className="d-flex gap-2">
                    {!notification.read && (
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <FontAwesomeIcon icon={faCheck} />
                      </Button>
                    )}
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => handleDelete(notification.id)}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </div>
                </div>
              </ListGroup.Item>
            ))
          ) : (
            <ListGroup.Item className="text-center py-4">
              No notifications
            </ListGroup.Item>
          )}
        </ListGroup>
      </Card>
    </Container>
  );
};

export default Notifications;
