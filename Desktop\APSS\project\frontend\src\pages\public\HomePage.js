import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>v, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Carousel, <PERSON><PERSON>, <PERSON><PERSON>, Accordion } from "react-bootstrap";
import "./Home.css";
import { useState, useEffect } from "react";
import axios from "axios";
import { Link } from 'react-router-dom';
import { API_BASE_URL } from "../../assets/utils/constants";
import { submitContactForm } from "../../assets/utils/api";
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faCalendarCheck, faUserMd, faHospital, 
  faShieldAlt, faClock, faEnvelope, 
  faPhone, faMapMarkerAlt 
} from "@fortawesome/free-solid-svg-icons";
import doctor1 from '../../assets/images/41808433_l.jpg';
import doctor2 from '../../assets/images/What-is-the-Role-of-a-Primary-Care-Physician.jpg';
import doctor3 from '../../assets/images/young-asian-female-dentist-white-coat-posing-clinic-equipment.jpg';
import doctor4 from '../../assets/images/about-doctor-speaking-with-medical-team.jpg';
import doctor5 from '../../assets/images/female-doctor-with-presenting-hand-gesture.jpg';
import doctor6 from '../../assets/images/portrait-successful-mid-adult-doctor-with-crossed-arms.jpg';

// Add icons to library
library.add(
  faCalendarCheck, faUserMd, faHospital, 
  faShieldAlt, faClock, faEnvelope, 
  faPhone, faMapMarkerAlt
);

const HomePage = () => {
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Contact form state
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [contactLoading, setContactLoading] = useState(false);
  const [contactSuccess, setContactSuccess] = useState("");
  const [contactError, setContactError] = useState("");

  useEffect(() => {
    // Use the correct API endpoint from constants
    setLoading(true);
    setError("");
    
    axios
      .get(`${API_BASE_URL}/api/doctors`)
      .then((response) => {
        console.log("Doctors data received:", response.data);
        if (response.data && Array.isArray(response.data)) {
          setDoctors(response.data);
        } else {
          console.error("Invalid doctors data format:", response.data);
          setError("Failed to load doctors data. Please try again later.");
          setDoctors([]);
        }
      })
      .catch((error) => {
        console.error("There was an error fetching the doctors data!", error);
        setError("Failed to load doctors. Please try again later.");
        setDoctors([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);
  
  // Handle contact form changes
  const handleContactChange = (e) => {
    setContactForm({
      ...contactForm,
      [e.target.name]: e.target.value
    });
  };
  
  // Handle contact form submission
  const handleContactSubmit = async (e) => {
    e.preventDefault();
    setContactLoading(true);
    setContactSuccess("");
    setContactError("");
    
    // Validate form fields
    if (!contactForm.name || !contactForm.email || !contactForm.message) {
      setContactError("Please fill in all required fields");
      setContactLoading(false);
      return;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contactForm.email)) {
      setContactError("Please enter a valid email address");
      setContactLoading(false);
      return;
    }
    
    try {
      console.log('Submitting contact form:', contactForm);
      const response = await submitContactForm(contactForm);
      console.log('Contact form submission response:', response);
      
      setContactSuccess("Your message has been sent successfully! We'll get back to you soon.");
      setContactForm({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      
      // Scroll to the success message
      setTimeout(() => {
        const successAlert = document.querySelector('#contact .alert-success');
        if (successAlert) {
          successAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    } catch (err) {
      console.error("Error sending contact form:", err);
      setContactError("Failed to send message. Please try again later. Error: " + (err.response?.data || err.message));
    } finally {
      setContactLoading(false);
    }
  };

  // Testimonials data
  const testimonials = [
    {
      id: 1,
      name: "Sarah Johnson",
      role: "Doctor",
      text: "The appointment booking system is incredibly user-friendly! I was able to schedule my appointment in less than a minute. Highly recommend!",
      avatar: "https://randomuser.me/api/portraits/women/32.jpg"
    },
    {
      id: 2,
      name: "Michael Rodriguez",
      role: "Patient",
      text: "I love how easy it is to find doctors by specialty and check their availability. This has made managing my family's healthcare so much simpler.",
      avatar: "https://randomuser.me/api/portraits/men/45.jpg"
    },
    {
      id: 3,
      name: "Dr. Emily Chen",
      role: "Cardiologist",
      text: "As a doctor, this system has streamlined my schedule and reduced no-shows significantly. The automatic reminders are a game-changer!",
      avatar: "https://randomuser.me/api/portraits/women/68.jpg"
    }
  ];

  return (
    <>
      {/* Sticky Navigation Bar */}
      <Navbar bg="white" expand="lg" fixed="top" className="shadow-sm py-3">
        <Container>
          <Navbar.Brand href="#home" className="fw-bold text-primary">
            <FontAwesomeIcon icon="hospital" className="me-2" />
            MediAppoint
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="navbarScroll" />
          <Navbar.Collapse id="navbarScroll">
            <Nav className="ms-auto">
              <Nav.Link href="#home" className="mx-2">Home</Nav.Link>
              <Nav.Link href="#about" className="mx-2">About</Nav.Link>
              <Nav.Link href="#services" className="mx-2">Services</Nav.Link>
              <Nav.Link href="#doctors" className="mx-2">Doctors</Nav.Link>
              <Nav.Link href="#testimonials" className="mx-2">Testimonials</Nav.Link>
              <Nav.Link href="#faq" className="mx-2">FAQ</Nav.Link>
              <Nav.Link href="#contact" className="mx-2">Contact</Nav.Link>
              <Nav.Link as={Link} to="/login" className="mx-2 btn btn-outline-primary">Log In</Nav.Link>
              <Nav.Link as={Link} to="/register" className="mx-2 btn btn-primary text-white">Sign Up</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      {/* Hero Section */}
      <section id="home" className="hero-section d-flex align-items-center">
        <Container>
          <Row className="align-items-center">
            <Col lg={6} className="mb-5 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">Your Health, <span className="text-primary">Our Priority</span></h1>
              <p className="lead mb-4">
                Book appointments with top healthcare professionals in just a few clicks. 
                Experience healthcare simplified.
              </p>
              <div className="d-flex flex-wrap gap-3">
                <Button as={Link} to="/register" variant="primary" size="lg" className="px-4 py-2">
                  Get Started
                </Button>
                <Button as={Link} to="/doctors" variant="outline-primary" size="lg" className="px-4 py-2">
                  Find Doctors
                </Button>
              </div>
            </Col>
            <Col lg={6}>
              <img 
                src="https://img.freepik.com/free-photo/doctor-with-stethoscope-hands-hospital-background_1423-1.jpg" 
                alt="Doctor with patient" 
                className="img-fluid rounded-lg shadow-lg"
              />
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="bg-light py-5">
        <Container>
          <Row className="text-center">
            <Col md={4} className="mb-4 mb-md-0">
              <div className="stat-item">
                <h2 className="display-4 fw-bold text-primary">5000+</h2>
                <p className="lead">Satisfied Patients</p>
              </div>
            </Col>
            <Col md={4} className="mb-4 mb-md-0">
              <div className="stat-item">
                <h2 className="display-4 fw-bold text-primary">100+</h2>
                <p className="lead">Expert Doctors</p>
              </div>
            </Col>
            <Col md={4}>
              <div className="stat-item">
                <h2 className="display-4 fw-bold text-primary">15+</h2>
                <p className="lead">Medical Specialties</p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* About Section */}
      <section id="about" className="section py-5">
        <Container>
          <Row className="justify-content-center mb-5">
            <Col lg={8} className="text-center">
              <h6 className="text-primary fw-bold mb-3">ABOUT US</h6>
              <h2 className="display-5 fw-bold mb-4">Simplifying Healthcare Access</h2>
              <p className="lead">
                MediAppoint is designed to bridge the gap between patients and healthcare providers, 
                making quality healthcare accessible to everyone through our intuitive appointment booking platform.
              </p>
            </Col>
          </Row>
          <Row className="g-4">
            <Col md={4}>
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4 text-center">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="calendar-check" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Easy Booking</h4>
                  <p className="text-muted">
                    Book appointments with your preferred doctors in just a few clicks, anytime and anywhere.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4 text-center">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="shield-alt" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Secure & Private</h4>
                  <p className="text-muted">
                    Your health data is protected with industry-standard security measures and encryption.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4 text-center">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="clock" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">24/7 Access</h4>
                  <p className="text-muted">
                    Schedule, reschedule, or cancel appointments anytime, anywhere with 24/7 platform access.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section id="services" className="section bg-light py-5">
        <Container>
          <Row className="justify-content-center mb-5">
            <Col lg={8} className="text-center">
              <h6 className="text-primary fw-bold mb-3">OUR SERVICES</h6>
              <h2 className="display-5 fw-bold mb-4">Comprehensive Healthcare Solutions</h2>
              <p className="lead">
                We offer a range of services designed to make healthcare management simple and efficient for both patients and doctors.
              </p>
            </Col>
          </Row>
          <Row className="g-4">
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="calendar-check" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Appointment Scheduling</h4>
                  <p className="text-muted">
                    Book appointments in just a few clicks using our smart calendar system.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="user-md" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Doctor Directory</h4>
                  <p className="text-muted">
                    Access profiles, specialties, and availability of all registered doctors.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="clock" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Consultation Reminders</h4>
                  <p className="text-muted">
                    Receive automatic reminders for upcoming appointments by email or SMS.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="hospital" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Virtual Consultations</h4>
                  <p className="text-muted">
                    Connect with doctors remotely through secure video consultations from anywhere.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="shield-alt" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Medical Records Access</h4>
                  <p className="text-muted">
                    Securely access and manage your medical history and treatment records online.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4} className="mb-4">
              <Card className="h-100 border-0 shadow-sm hover-card">
                <Card.Body className="p-4">
                  <div className="icon-box mb-4">
                    <FontAwesomeIcon icon="envelope" className="fa-2x text-primary" />
                  </div>
                  <h4 className="mb-3">Prescription Management</h4>
                  <p className="text-muted">
                    Request prescription refills and receive digital prescriptions directly from your doctor.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Doctors Section */}
      <section id="doctors" className="section bg-white">
        <Container>
          <h2 className="text-center mb-5 text-primary">Meet Our Doctors</h2>
          <div className="row text-center">
            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor1}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. John Doe</h5>
                  <p className="card-text">Specialty: Cardiology</p>
                  
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor2}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. Jane Smith</h5>
                  <p className="card-text">Specialty: Dermatology</p>
                  
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor3}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. Mary Johnson</h5>
                  <p className="card-text">Specialty: Neurology</p>
                  
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor4}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. Michael Brown</h5>
                  <p className="card-text">Specialty: Orthopedics</p>
                 
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor5}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. Sarah Lee</h5>
                  <p className="card-text">Specialty: Pediatrics</p>
                  
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div className="card h-100 border-0 shadow-sm">
                <img
                  src={doctor6}
                  className="card-img-top rounded-circle mx-auto d-block"
                  alt="Doctor"
                />
                <div className="card-body">
                  <h5 className="card-title">Dr. David Green</h5>
                  <p className="card-text">Specialty: General Medicine</p>
                  
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="section bg-light py-5">
        <Container>
          <Row className="justify-content-center mb-5">
            <Col lg={8} className="text-center">
              <h6 className="text-primary fw-bold mb-3">TESTIMONIALS</h6>
              <h2 className="display-5 fw-bold mb-4">What Our Users Say</h2>
              <p className="lead">
                Discover how MediAppoint has transformed healthcare experiences for patients and doctors alike.
              </p>
            </Col>
          </Row>
          
          <Carousel 
            indicators={false} 
            className="testimonial-carousel"
            interval={5000}
          >
            {testimonials.map((testimonial) => (
              <Carousel.Item key={testimonial.id}>
                <div className="testimonial-item text-center">
                  <img 
                    src={testimonial.avatar} 
                    alt={testimonial.name} 
                    className="testimonial-img rounded-circle mx-auto mb-4"
                    width="100"
                  />
                  <div className="testimonial-content">
                    <p className="testimonial-text mb-4">"{testimonial.text}"</p>
                    <h5 className="testimonial-name mb-1">{testimonial.name}</h5>
                    <p className="testimonial-role text-primary">{testimonial.role}</p>
                  </div>
                </div>
              </Carousel.Item>
            ))}
          </Carousel>
        </Container>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="section py-5">

        <Container>
          <Row className="justify-content-center mb-5">
            <Col lg={8} className="text-center">
              <h6 className="text-primary fw-bold mb-3">FREQUENTLY ASKED QUESTIONS</h6>
              <h2 className="display-5 fw-bold mb-4">Common Questions</h2>
              <p className="lead">
                Find answers to the most common questions about our appointment scheduling system.
              </p>
            </Col>
          </Row>

          <Row className="justify-content-center">
            <Col lg={10}>
              <Accordion defaultActiveKey="0" className="faq-accordion">
                <Accordion.Item eventKey="0" className="mb-3 shadow-sm border-0">
                  <Accordion.Header>
                    <span className="me-2"><FontAwesomeIcon icon={faCalendarCheck} className="text-primary" /></span>
                    How do I schedule an appointment?
                  </Accordion.Header>
                  <Accordion.Body>
                    To schedule an appointment, first create an account or log in. Then, browse our list of doctors, select your preferred doctor, and choose an available time slot from their calendar. Confirm your appointment details and you're all set!
                  </Accordion.Body>
                </Accordion.Item>

                <Accordion.Item eventKey="1" className="mb-3 shadow-sm border-0">
                  <Accordion.Header>
                    <span className="me-2"><FontAwesomeIcon icon={faClock} className="text-primary" /></span>
                    Can I reschedule or cancel my appointment?
                  </Accordion.Header>
                  <Accordion.Body>
                    Yes, you can reschedule or cancel your appointment through your patient dashboard. We recommend making any changes at least 24 hours before your scheduled appointment time to allow other patients to book that slot.
                  </Accordion.Body>
                </Accordion.Item>

                <Accordion.Item eventKey="2" className="mb-3 shadow-sm border-0">
                  <Accordion.Header>
                    <span className="me-2"><FontAwesomeIcon icon={faUserMd} className="text-primary" /></span>
                    How do I find a doctor with a specific specialty?
                  </Accordion.Header>
                  <Accordion.Body>
                    You can use the search and filter options on our "Find Doctors" page to locate doctors with specific specialties. You can filter by specialty, availability, or search by doctor name.
                  </Accordion.Body>
                </Accordion.Item>

                <Accordion.Item eventKey="3" className="mb-3 shadow-sm border-0">
                  <Accordion.Header>
                    <span className="me-2"><FontAwesomeIcon icon={faEnvelope} className="text-primary" /></span>
                    Will I receive a reminder before my appointment?
                  </Accordion.Header>
                  <Accordion.Body>
                    Yes, our system automatically sends appointment reminders 24 hours before your scheduled appointment via email. You can also opt to receive SMS reminders in your account settings.
                  </Accordion.Body>
                </Accordion.Item>

                <Accordion.Item eventKey="4" className="mb-3 shadow-sm border-0">
                  <Accordion.Header>
                    <span className="me-2"><FontAwesomeIcon icon={faShieldAlt} className="text-primary" /></span>
                    Is my personal and medical information secure?
                  </Accordion.Header>
                  <Accordion.Body>
                    Absolutely. We take data security very seriously. All personal and medical information is encrypted and stored securely following industry best practices and compliance standards. We never share your information with third parties without your explicit consent.
                  </Accordion.Body>
                </Accordion.Item>
              </Accordion>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Contact Section */}
      <section id="contact" className="section py-5">
        <Container>
          <Row className="justify-content-center mb-5">
            <Col lg={8} className="text-center">
              <h6 className="text-primary fw-bold mb-3">CONTACT US</h6>
              <h2 className="display-5 fw-bold mb-4">Get In Touch</h2>
              <p className="lead">
                Have questions or need assistance? We're here to help! Reach out to our team.
              </p>
            </Col>
          </Row>
          
          <Row className="g-5">
            <Col lg={5}>
              <div className="contact-info">
                <h4 className="mb-4">Contact Information</h4>
                <div className="contact-item d-flex mb-4">
                  <div className="icon-box me-3">
                    <FontAwesomeIcon icon="map-marker-alt" className="fa-fw text-primary" />
                  </div>
                  <div>
                    <h5 className="mb-1">Address</h5>
                    <p className="mb-0">Avenue des banks , Yaounde-Cameroun, 10001</p>
                  </div>
                </div>
                
                <div className="contact-item d-flex mb-4">
                  <div className="icon-box me-3">
                    <FontAwesomeIcon icon="phone" className="fa-fw text-primary" />
                  </div>
                  <div>
                    <h5 className="mb-1">Phone</h5>
                    <p className="mb-0">+237 *********</p>
                  </div>
                </div>
                
                <div className="contact-item d-flex mb-4">
                  <div className="icon-box me-3">
                    <FontAwesomeIcon icon="envelope" className="fa-fw text-primary" />
                  </div>
                  <div>
                    <h5 className="mb-1">Email</h5>
                    <p className="mb-0"><EMAIL></p>
                  </div>
                </div>
              </div>
            </Col>
            
            <Col lg={7}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-5">
                  <h4 className="mb-4">Send Us a Message</h4>
                  {contactSuccess && <Alert variant="success">{contactSuccess}</Alert>}
                  {contactError && <Alert variant="danger">{contactError}</Alert>}
                  <form onSubmit={handleContactSubmit}>
                    <Row>
                      <Col md={6} className="mb-3">
                        <div className="form-group">
                          <label htmlFor="name" className="form-label">Your Name</label>
                          <input 
                            type="text" 
                            className="form-control" 
                            id="name" 
                            name="name"
                            value={contactForm.name}
                            onChange={handleContactChange}
                            placeholder="Enter your name" 
                            required
                          />
                        </div>
                      </Col>
                      <Col md={6} className="mb-3">
                        <div className="form-group">
                          <label htmlFor="email" className="form-label">Your Email</label>
                          <input 
                            type="email" 
                            className="form-control" 
                            id="email" 
                            name="email"
                            value={contactForm.email}
                            onChange={handleContactChange}
                            placeholder="Enter your email" 
                            required
                          />
                        </div>
                      </Col>
                    </Row>
                    <div className="form-group mb-3">
                      <label htmlFor="subject" className="form-label">Subject</label>
                      <input 
                        type="text" 
                        className="form-control" 
                        id="subject" 
                        name="subject"
                        value={contactForm.subject}
                        onChange={handleContactChange}
                        placeholder="Enter subject" 
                        required
                      />
                    </div>
                    <div className="form-group mb-4">
                      <label htmlFor="message" className="form-label">Message</label>
                      <textarea 
                        className="form-control" 
                        id="message" 
                        name="message"
                        value={contactForm.message}
                        onChange={handleContactChange}
                        rows="5" 
                        placeholder="Enter your message"
                        required
                      ></textarea>
                    </div>
                    <Button 
                      variant="primary" 
                      type="submit" 
                      className="px-4 py-2"
                      disabled={contactLoading}
                    >
                      {contactLoading ? <Spinner animation="border" size="sm" /> : "Send Message"}
                    </Button>
                  </form>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Footer */}
      <footer className="bg-dark text-white py-5">
        <Container>
          <Row className="g-4">
            <Col lg={4} className="mb-4 mb-lg-0">
              <h4 className="mb-4">MediAppoint</h4>
              <p className="mb-4">
                Simplifying healthcare access through our intuitive appointment booking platform.
              </p>
              <div className="social-links">
                <a href="#!" className="me-3 text-white"><i className="bi bi-facebook"></i></a>
                <a href="#!" className="me-3 text-white"><i className="bi bi-twitter"></i></a>
                <a href="#!" className="me-3 text-white"><i className="bi bi-instagram"></i></a>
                <a href="#!" className="text-white"><i className="bi bi-linkedin"></i></a>
              </div>
            </Col>
            <Col lg={2} md={6} className="mb-4 mb-lg-0">
              <h5 className="mb-4">Quick Links</h5>
              <ul className="list-unstyled footer-links">
                <li className="mb-2"><a href="#home" className="text-white text-decoration-none">Home</a></li>
                <li className="mb-2"><a href="#about" className="text-white text-decoration-none">About</a></li>
                <li className="mb-2"><a href="#services" className="text-white text-decoration-none">Services</a></li>
                <li className="mb-2"><a href="#doctors" className="text-white text-decoration-none">Doctors</a></li>
                <li className="mb-2"><a href="#testimonials" className="text-white text-decoration-none">Testimonials</a></li>
                <li className="mb-2"><a href="#faq" className="text-white text-decoration-none">FAQ</a></li>
                <li className="mb-2"><a href="#contact" className="text-white text-decoration-none">Contact</a></li>
              </ul>
            </Col>
            <Col lg={3} md={6} className="mb-4 mb-lg-0">
              <h5 className="mb-4">Services</h5>
              <ul className="list-unstyled footer-links">
                <li className="mb-2"><a href="#!" className="text-white text-decoration-none">Appointment Booking</a></li>
                <li className="mb-2"><a href="#!" className="text-white text-decoration-none">Doctor Consultations</a></li>
                <li className="mb-2"><a href="#!" className="text-white text-decoration-none">Medical Records</a></li>
                <li className="mb-2"><a href="#!" className="text-white text-decoration-none">Health Reminders</a></li>
              </ul>
            </Col>
            <Col lg={3} md={6}>
              <h5 className="mb-4">Newsletter</h5>
              <p className="mb-4">Subscribe to our newsletter for health tips and updates.</p>
              <form className="mb-3">
                <div className="input-group">
                  <input type="email" className="form-control" placeholder="Your email" />
                  <Button variant="primary" type="submit">Subscribe</Button>
                </div>
              </form>
            </Col>
          </Row>
          <hr className="my-4" />
          <div className="text-center">
            <p className="mb-0">&copy; {new Date().getFullYear()} MediAppoint. All rights reserved.</p>
          </div>
        </Container>
      </footer>
    </>
  );
};

export default HomePage;
