import React from "react";
import { Route, Routes } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import HomePage from "./pages/public/HomePage";
import About from "./pages/public/About";
import Contact from "./pages/public/Contact";
import SpecialtyList from "./pages/SpecialtyList";
import FAQ from "./pages/public/FAQ";
import PatientDashboard from "./pages/patient/Dashboard";
import DoctorDashboard from "./pages/doctor/Dashboard";
import AdminDashboard from "./pages/admin/Dashboard";
import ErrorPage from "./pages/public/ErrorPage";
import ProtectedRoute from "./routes/ProtectedRoute";
import Navbar from "./components/common/Navbar";
import Layout from "./components/common/Layout";
import DoctorProfile from "./pages/doctor/DoctorProfile";
import DoctorList from "./pages/public/DoctorList";
import DoctorDetail from "./pages/public/DoctorDetail";
import BookAppointment from "./pages/patient/BookAppointment";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import ForgotPassword from "./pages/auth/ForgotPassword";
import UserProfile from "./components/common/UserProfile";
import PatientNotifications from "./pages/patient/Notifications";
import AppointmentDetails from "./pages/patient/AppointmentsDetails";
import AvailabilityManagement from "./pages/doctor/AvailabilityManagement";
import Payments from "./pages/patient/Payments";
import AppointmentHistory from "./pages/patient/AppointmentHistory";
import MedicalRecord from "./pages/patient/MedicalRecord";
import Message from "./pages/patient/Message";
import Feedback from "./pages/patient/Feedback";

const App = () => {
  return (
    <>
      <Navbar />
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<HomePage />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/login" element={<Login />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/specialtyList" element={<SpecialtyList />} />
        <Route path="/register" element={<Register />} />
        <Route path="/faq" element={<FAQ />} />
        <Route path="/doctors" element={<DoctorList />} />
        <Route path="/doctors/:doctorId" element={<DoctorDetail />} />

        {/* Protected Routes with Layout */}
        <Route
          path="/patient/dashboard"
          element={<ProtectedRoute role="PATIENT"><Layout><PatientDashboard /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/book-appointment"
          element={<ProtectedRoute role="PATIENT"><Layout><BookAppointment /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/profile"
          element={<ProtectedRoute role="PATIENT"><Layout><UserProfile /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/notifications"
          element={<ProtectedRoute role="PATIENT"><Layout><PatientNotifications /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/appointments/:appointmentId/details"
          element={<ProtectedRoute role="PATIENT"><Layout><AppointmentDetails /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/appointments"
          element={<ProtectedRoute role="PATIENT"><Layout><AppointmentHistory /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/records"
          element={<ProtectedRoute role="PATIENT"><Layout><MedicalRecord /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/messages"
          element={<ProtectedRoute role="PATIENT"><Layout><Message /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/feedback"
          element={<ProtectedRoute role="PATIENT"><Layout><Feedback /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/doctors"
          element={<ProtectedRoute role="PATIENT"><Layout><DoctorList /></Layout></ProtectedRoute>}
        />
        <Route
          path="/patient/payments"
          element={<ProtectedRoute role="PATIENT"><Layout><Payments /></Layout></ProtectedRoute>}
        />
        <Route
          path="/doctor/dashboard"
          element={<ProtectedRoute role="DOCTOR"><Layout><DoctorDashboard /></Layout></ProtectedRoute>}
        />
        <Route
          path="/doctor/profile"
          element={<ProtectedRoute role="DOCTOR"><Layout><UserProfile /></Layout></ProtectedRoute>}
        />
        <Route
          path="/doctor/availability"
          element={<ProtectedRoute role="DOCTOR"><Layout><AvailabilityManagement /></Layout></ProtectedRoute>}
        />
        <Route
          path="/admin/dashboard"
          element={<ProtectedRoute role="ADMIN"><Layout><AdminDashboard /></Layout></ProtectedRoute>}
        />
        <Route
          path="/admin/profile"
          element={<ProtectedRoute role="ADMIN"><Layout><UserProfile /></Layout></ProtectedRoute>}
        />

        {/* 404 Page */}
        <Route path="*" element={<ErrorPage />} />
      </Routes>
    </>
  );
};

export default App;
