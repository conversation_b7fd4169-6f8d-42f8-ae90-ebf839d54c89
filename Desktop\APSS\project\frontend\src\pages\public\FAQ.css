.faq-container {
  max-width: 1200px;
  margin: 0 auto;
}

.faq-accordion .accordion-button {
  font-weight: 600;
  padding: 1.25rem;
  background-color: #fff;
  color: #333;
  transition: all 0.3s ease;
}

.faq-accordion .accordion-button:not(.collapsed) {
  background-color: #f8f9fa;
  color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
}

.faq-accordion .accordion-button:focus {
  box-shadow: none;
  border-color: rgba(13, 110, 253, 0.25);
}

.faq-accordion .accordion-body {
  padding: 1.5rem;
  background-color: #f8f9fa;
  line-height: 1.6;
  transition: max-height 0.5s ease;
}

/* Animation for the accordion */
.faq-accordion .accordion-collapse {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transform: translateY(-10px);
}

.faq-accordion .accordion-collapse.show {
  max-height: 1000px; /* Large enough to contain content */
  opacity: 1;
  transform: translateY(0);
}

.faq-accordion .accordion-item {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.faq-accordion .accordion-header {
  margin-bottom: 0;
}

/* Icon rotation animation */
.faq-accordion .accordion-button::after {
  transition: transform 0.3s ease-in-out;
}

/* Additional styling for the contact card */
.faq-container .card {
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faq-container .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Additional styling for answer content */
.answer-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
