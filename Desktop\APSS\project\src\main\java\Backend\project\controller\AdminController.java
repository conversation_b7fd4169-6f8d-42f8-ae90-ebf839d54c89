package Backend.project.controller;

import Backend.project.model.*;
import Backend.project.model.Role;
import Backend.project.payload.request.*;
import Backend.project.repositories.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasAuthority('ADMIN')")
public class AdminController {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Autowired
    private SpecialtyRepository specialtyRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    // Dashboard statistics
    @GetMapping("/stats")
    public ResponseEntity<?> getDashboardStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // Count users by role
            long totalUsers = userRepository.count();
            long totalDoctors = userRepository.countByRole(Role.DOCTOR.toString());
            long totalPatients = userRepository.countByRole(Role.PATIENT.toString());
            long totalAdmins = userRepository.countByRole(Role.ADMIN.toString());
            
            // Count appointments by status
            long totalAppointments = appointmentRepository.count();
            long pendingAppointments = 0;
            long confirmedAppointments = 0;
            long completedAppointments = 0;
            long canceledAppointments = 0;
            
            // Safely count appointments by status
            try {
                pendingAppointments = appointmentRepository.countByStatus(AppointmentStatus.PENDING.toString());
                confirmedAppointments = appointmentRepository.countByStatus(AppointmentStatus.CONFIRMED.toString());
                completedAppointments = appointmentRepository.countByStatus(AppointmentStatus.COMPLETED.toString());
                canceledAppointments = appointmentRepository.countByStatus(AppointmentStatus.CANCELED.toString());
            } catch (Exception e) {
                // Log the error but continue with zeros
                System.err.println("Error counting appointments by status: " + e.getMessage());
            }
            
            stats.put("totalUsers", totalUsers);
            stats.put("totalDoctors", totalDoctors);
            stats.put("totalPatients", totalPatients);
            stats.put("totalAdmins", totalAdmins);
            stats.put("totalAppointments", totalAppointments);
            stats.put("pendingAppointments", pendingAppointments);
            stats.put("confirmedAppointments", confirmedAppointments);
            stats.put("completedAppointments", completedAppointments);
            stats.put("canceledAppointments", canceledAppointments);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            System.err.println("Error generating dashboard stats: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error generating dashboard statistics: " + e.getMessage());
        }
    }
    
    // User management
    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userRepository.findAll();
        return ResponseEntity.ok(users);
    }
    
    // Patient management
    @GetMapping("/patients")
    public ResponseEntity<List<Patient>> getAllPatients() {
        List<Patient> patients = patientRepository.findAll();
        return ResponseEntity.ok(patients);
    }
    
    @GetMapping("/users/{id}")
    public ResponseEntity<?> getUserById(@PathVariable Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isPresent()) {
            return ResponseEntity.ok(userOpt.get());
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
    }
    
    @PostMapping("/users")
    public ResponseEntity<?> createUser(@RequestBody UserRequest userRequest) {
        // Check if user already exists
        if (userRepository.existsByEmail(userRequest.getEmail())) {
            return ResponseEntity.badRequest().body("Email already in use");
        }
        
        // Create new user
        User user = new User(
            userRequest.getName(),
            userRequest.getEmail(),
            passwordEncoder.encode(userRequest.getPassword()),
            userRequest.getRole()
        );
        
        User savedUser = userRepository.save(user);
        
        // If doctor or patient, create corresponding entity
        if ("DOCTOR".equals(userRequest.getRole()) && userRequest.getSpecialtyId() != null) {
            Optional<Specialty> specialtyOpt = specialtyRepository.findById(userRequest.getSpecialtyId());
            if (specialtyOpt.isPresent()) {
                Doctor doctor = new Doctor();
                doctor.setUser(savedUser);
                doctor.setSpecialty(specialtyOpt.get());
                doctorRepository.save(doctor);
            }
        } else if (Role.PATIENT.name().equals(userRequest.getRole())) {
            Patient patient = new Patient();
            patient.setUser(savedUser);
            patientRepository.save(patient);
        }
        
        return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
    }
    
    @PutMapping("/users/{id}")
    public ResponseEntity<?> updateUser(@PathVariable Long id, @RequestBody UserRequest userRequest) {
        Optional<User> userOpt = userRepository.findById(id);
        if (!userOpt.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
        }
        
        User user = userOpt.get();
        user.setName(userRequest.getName());
        
        // Only update email if it's changed and not already in use
        if (!user.getEmail().equals(userRequest.getEmail())) {
            if (userRepository.existsByEmail(userRequest.getEmail())) {
                return ResponseEntity.badRequest().body("Email already in use");
            }
            user.setEmail(userRequest.getEmail());
        }
        
        // Update password if provided
        if (userRequest.getPassword() != null && !userRequest.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userRequest.getPassword()));
        }
        
        // Update role if changed
        if (!user.getRole().equals(userRequest.getRole())) {
            user.setRole(userRequest.getRole());
            
            // Handle role change implications
            if ("DOCTOR".equals(userRequest.getRole())) {
                // Convert to doctor
                if (userRequest.getSpecialtyId() != null) {
                    Optional<Specialty> specialtyOpt = specialtyRepository.findById(userRequest.getSpecialtyId());
                    if (specialtyOpt.isPresent()) {
                        Doctor doctor = doctorRepository.findByUserId(id).orElse(new Doctor());
                        doctor.setUser(user);
                        doctor.setSpecialty(specialtyOpt.get());
                        doctorRepository.save(doctor);
                    }
                }
            } else if (Role.PATIENT.name().equals(userRequest.getRole())) {
                // Convert to patient
                Patient patient = patientRepository.findByUserId(id).orElse(new Patient());
                patient.setUser(user);
                patientRepository.save(patient);
            }
        }
        
        User updatedUser = userRepository.save(user);
        return ResponseEntity.ok(updatedUser);
    }
    
    @DeleteMapping("/users/{id}")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        if (!userRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
        }
        
        // Delete user and related entities
        userRepository.deleteById(id);
        return ResponseEntity.ok("User deleted successfully");
    }
    
    // Doctor management
    @GetMapping("/doctors")
    public ResponseEntity<?> getAllDoctors() {
        List<Doctor> doctors = doctorRepository.findAll();
        return ResponseEntity.ok(doctors);
    }
    
    // Specialty management
    @GetMapping("/specialties")
    public ResponseEntity<List<Specialty>> getAllSpecialties() {
        List<Specialty> specialties = specialtyRepository.findAll();
        return ResponseEntity.ok(specialties);
    }
    
    @GetMapping("/specialties/{id}")
    public ResponseEntity<?> getSpecialtyById(@PathVariable Long id) {
        Optional<Specialty> specialtyOpt = specialtyRepository.findById(id);
        if (specialtyOpt.isPresent()) {
            return ResponseEntity.ok(specialtyOpt.get());
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Specialty not found");
    }
    
    @PostMapping("/specialties")
    public ResponseEntity<?> createSpecialty(@RequestBody SpecialtyRequest specialtyRequest) {
        // Check if specialty already exists
        if (specialtyRepository.existsByName(specialtyRequest.getName())) {
            return ResponseEntity.badRequest().body("Specialty already exists");
        }
        
        // Create new specialty
        Specialty specialty = new Specialty();
        specialty.setName(specialtyRequest.getName());
        specialty.setDescription(specialtyRequest.getDescription());
        
        Specialty savedSpecialty = specialtyRepository.save(specialty);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedSpecialty);
    }
    
    @PutMapping("/specialties/{id}")
    public ResponseEntity<?> updateSpecialty(@PathVariable Long id, @RequestBody SpecialtyRequest specialtyRequest) {
        Optional<Specialty> specialtyOpt = specialtyRepository.findById(id);
        if (!specialtyOpt.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Specialty not found");
        }
        
        Specialty specialty = specialtyOpt.get();
        
        // Check if name is changed and not already in use
        if (!specialty.getName().equals(specialtyRequest.getName())) {
            if (specialtyRepository.existsByName(specialtyRequest.getName())) {
                return ResponseEntity.badRequest().body("Specialty name already in use");
            }
            specialty.setName(specialtyRequest.getName());
        }
        
        specialty.setDescription(specialtyRequest.getDescription());
        
        Specialty updatedSpecialty = specialtyRepository.save(specialty);
        return ResponseEntity.ok(updatedSpecialty);
    }
    
    @DeleteMapping("/specialties/{id}")
    public ResponseEntity<?> deleteSpecialty(@PathVariable Long id) {
        if (!specialtyRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Specialty not found");
        }
        
        // Check if specialty is in use by any doctor
        if (doctorRepository.existsBySpecialtyId(id)) {
            return ResponseEntity.badRequest().body("Cannot delete specialty that is assigned to doctors");
        }
        
        specialtyRepository.deleteById(id);
        return ResponseEntity.ok("Specialty deleted successfully");
    }
    
    // Appointment management
    @GetMapping("/appointments")
    public ResponseEntity<List<Appointment>> getAllAppointments() {
        List<Appointment> appointments = appointmentRepository.findAll();
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/appointments/{id}")
    public ResponseEntity<?> getAppointmentById(@PathVariable Long id) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(id);
        if (appointmentOpt.isPresent()) {
            return ResponseEntity.ok(appointmentOpt.get());
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Appointment not found");
    }
    
    @PutMapping("/appointments/{id}/status")
    public ResponseEntity<?> updateAppointmentStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(id);
        if (!appointmentOpt.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Appointment not found");
        }
        
        String newStatus = statusUpdate.get("status");
        if (newStatus == null || newStatus.isEmpty()) {
            return ResponseEntity.badRequest().body("Status is required");
        }
        
        try {
            AppointmentStatus status = AppointmentStatus.valueOf(newStatus);
            Appointment appointment = appointmentOpt.get();
            appointment.setStatus(status);
            
            Appointment updatedAppointment = appointmentRepository.save(appointment);
            return ResponseEntity.ok(updatedAppointment);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Invalid status value. Valid values are: " + 
                    java.util.Arrays.toString(AppointmentStatus.values()));
        }
    }
    
    @DeleteMapping("/appointments/{id}")
    public ResponseEntity<?> deleteAppointment(@PathVariable Long id) {
        if (!appointmentRepository.existsById(id)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Appointment not found");
        }
        
        appointmentRepository.deleteById(id);
        return ResponseEntity.ok("Appointment deleted successfully");
    }
}
