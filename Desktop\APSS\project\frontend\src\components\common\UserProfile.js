import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Form, <PERSON><PERSON>, Al<PERSON>, <PERSON><PERSON>, Badge } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faUser, 
  faEnvelope, 
  faPhone, 
  faMapMarkerAlt, 
  faSave, 
  faEdit,
  faUserMd,
  faUserShield
} from "@fortawesome/free-solid-svg-icons";
import { useAuth } from "../../context/AuthContext";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import "./UserProfile.css";

const UserProfile = () => {
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    bio: "",
    specialty: "",
    experience: "",
    licenseNumber: ""
  });

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        bio: user.bio || "",
        specialty: user.specialty || "",
        experience: user.experience || "",
        licenseNumber: user.licenseNumber || ""
      });
    }
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const response = await axios.put(
        `${API_BASE_URL}/api/users/${user.id}/profile`,
        profileData,
        {
          headers: { Authorization: `Bearer ${user.token}` }
        }
      );

      setSuccess("Profile updated successfully!");
      setIsEditing(false);
      
      // Update local storage with new user data
      const updatedUser = { ...user, ...profileData };
      localStorage.setItem("user", JSON.stringify(updatedUser));
      
    } catch (err) {
      console.error("Error updating profile:", err);
      setError("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const getRoleIcon = () => {
    switch (user?.role?.toLowerCase()) {
      case "doctor":
        return <FontAwesomeIcon icon={faUserMd} className="text-primary" />;
      case "admin":
        return <FontAwesomeIcon icon={faUserShield} className="text-danger" />;
      default:
        return <FontAwesomeIcon icon={faUser} className="text-success" />;
    }
  };

  const getRoleBadge = () => {
    const role = user?.role?.toLowerCase();
    switch (role) {
      case "doctor":
        return <Badge bg="primary">Doctor</Badge>;
      case "admin":
        return <Badge bg="danger">Administrator</Badge>;
      case "patient":
        return <Badge bg="success">Patient</Badge>;
      default:
        return <Badge bg="secondary">{user?.role}</Badge>;
    }
  };

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FontAwesomeIcon icon={faUser} className="me-2" />
            Profile Settings
          </h2>
          <p className="text-muted">Manage your account information and preferences</p>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" onClose={() => setSuccess("")} dismissible>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Personal Information</h5>
              <Button
                variant={isEditing ? "secondary" : "primary"}
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
              >
                <FontAwesomeIcon icon={isEditing ? faEdit : faEdit} className="me-1" />
                {isEditing ? "Cancel" : "Edit"}
              </Button>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        Full Name
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={profileData.name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                        Email Address
                      </Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={profileData.email}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faPhone} className="me-2" />
                        Phone Number
                      </Form.Label>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={profileData.phone}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2" />
                        Address
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="address"
                        value={profileData.address}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {/* Doctor-specific fields */}
                {user?.role?.toLowerCase() === "doctor" && (
                  <>
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Specialty</Form.Label>
                          <Form.Control
                            type="text"
                            name="specialty"
                            value={profileData.specialty}
                            onChange={handleInputChange}
                            disabled={!isEditing}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>Years of Experience</Form.Label>
                          <Form.Control
                            type="text"
                            name="experience"
                            value={profileData.experience}
                            onChange={handleInputChange}
                            disabled={!isEditing}
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label>License Number</Form.Label>
                          <Form.Control
                            type="text"
                            name="licenseNumber"
                            value={profileData.licenseNumber}
                            onChange={handleInputChange}
                            disabled={!isEditing}
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </>
                )}

                <Form.Group className="mb-3">
                  <Form.Label>Bio</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="bio"
                    value={profileData.bio}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    placeholder="Tell us about yourself..."
                  />
                </Form.Group>

                {isEditing && (
                  <div className="d-flex gap-2">
                    <Button type="submit" variant="primary" disabled={saving}>
                      {saving ? (
                        <>
                          <Spinner animation="border" size="sm" className="me-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <FontAwesomeIcon icon={faSave} className="me-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                    <Button 
                      variant="secondary" 
                      onClick={() => setIsEditing(false)}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">Account Information</h5>
            </Card.Header>
            <Card.Body>
              <div className="text-center mb-3">
                <div className="avatar-placeholder mb-3">
                  {getRoleIcon()}
                </div>
                <h5>{user?.name}</h5>
                {getRoleBadge()}
              </div>

              <div className="account-info">
                <div className="info-item">
                  <strong>User ID:</strong> {user?.id}
                </div>
                <div className="info-item">
                  <strong>Email:</strong> {user?.email}
                </div>
                <div className="info-item">
                  <strong>Role:</strong> {user?.role}
                </div>
                <div className="info-item">
                  <strong>Member Since:</strong> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                </div>
              </div>

              <hr />

              <div className="d-grid gap-2">
                <Button variant="outline-primary" size="sm">
                  Change Password
                </Button>
                <Button variant="outline-secondary" size="sm">
                  Privacy Settings
                </Button>
                <Button variant="outline-danger" size="sm" onClick={logout}>
                  Sign Out
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default UserProfile;
