html {
  scroll-behavior: smooth;
}

.section {
  padding: 100px 0;
}

.full-height {
  height: 100vh;
}

.bg-home {
  background-image: url("../../assets/images/doctor-s-hand-holding-stethoscope-closeup.jpg");
  background-size: cover;
  background-position: center;
}

/* Hero Section */
.hero-section {
  padding-top: 150px;
  padding-bottom: 100px;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

/* Card Hover Effects */
.hover-card {
  transition: all 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Doctor Card Styling */
.doctor-card {
  overflow: hidden;
  transition: all 0.3s ease;
}

.doctor-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.doctor-img-container {
  height: 250px;
  overflow: hidden;
}

.doctor-img {
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.doctor-card:hover .doctor-img {
  transform: scale(1.05);
}

/* Testimonial Styling */
.testimonial-carousel {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border: 5px solid #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  font-size: 1.1rem;
  font-style: italic;
  color: #555;
  max-width: 800px;
  margin: 0 auto;
}

/* Stats Styling */
.stat-item {
  padding: 30px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background-color: #fff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

/* Contact Form Styling */
.form-control {
  padding: 12px;
  border-radius: 8px;
}

.form-control:focus {
  box-shadow: none;
  border-color: #0d6efd;
}

/* Icon Box Styling */
.icon-box {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(13, 110, 253, 0.1);
  color: #0d6efd;
}

/* Footer Styling */
.footer-links a {
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: #0d6efd !important;
  padding-left: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .section {
    padding: 60px 0;
  }
  
  .hero-section {
    padding-top: 120px;
    padding-bottom: 60px;
  }
  
  .doctor-img-container {
    height: 200px;
  }
}

/* Navbar Styling */
.navbar {
  transition: all 0.3s ease;
}

.navbar-brand {
  font-size: 1.5rem;
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #0d6efd !important;
}
