# PowerShell script to test authentication endpoints

$baseUrl = "http://localhost:8080/api/auth"

# Test registration
$registerData = @{
    name = "Test User"
    email = "<EMAIL>"
    password = "password123"
    role = "PATIENT"
} | ConvertTo-Json

Write-Host "Testing registration..." -ForegroundColor Cyan
$registerResponse = Invoke-RestMethod -Uri "$baseUrl/register" -Method Post -Body $registerData -ContentType "application/json" -ErrorAction SilentlyContinue
if ($registerResponse) {
    Write-Host "Registration successful!" -ForegroundColor Green
    Write-Host "Token: $($registerResponse.token)" -ForegroundColor Green
    Write-Host "User ID: $($registerResponse.id)" -ForegroundColor Green
    Write-Host "Role: $($registerResponse.role)" -ForegroundColor Green
} else {
    Write-Host "Registration failed!" -ForegroundColor Red
}

# Test login
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-<PERSON>son

Write-Host "`nTesting login..." -ForegroundColor Cyan
$loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method Post -Body $loginData -ContentType "application/json" -ErrorAction SilentlyContinue
if ($loginResponse) {
    Write-Host "Login successful!" -ForegroundColor Green
    Write-Host "Token: $($loginResponse.token)" -ForegroundColor Green
    Write-Host "User ID: $($loginResponse.id)" -ForegroundColor Green
    Write-Host "Role: $($loginResponse.role)" -ForegroundColor Green
} else {
    Write-Host "Login failed!" -ForegroundColor Red
}

# Test login with wrong password
$wrongLoginData = @{
    email = "<EMAIL>"
    password = "wrongpassword"
} | ConvertTo-Json

Write-Host "`nTesting login with wrong password..." -ForegroundColor Cyan
try {
    $wrongLoginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method Post -Body $wrongLoginData -ContentType "application/json" -ErrorAction Stop
    Write-Host "Login should have failed but succeeded!" -ForegroundColor Red
} catch {
    Write-Host "Login with wrong password correctly failed with status: $($_.Exception.Response.StatusCode)" -ForegroundColor Green
}
