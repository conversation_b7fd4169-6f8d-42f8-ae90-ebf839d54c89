import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Container, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { register } from "../../assets/utils/api";
import { USER_ROLES } from "../../assets/utils/constants";

const Register = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: USER_ROLES.PATIENT.toUpperCase(),
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match!");
      return;
    }
    
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await register({
        name: formData.fullName,
        email: formData.email,
        password: formData.password,
        role: formData.role
      });

      setSuccess("Registration successful! Redirecting to login page...");
      
      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      console.error("Registration error:", err);
      if (err.response && err.response.data) {
        setError(err.response.data);
      } else {
        setError("Registration failed. Please try again later.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="d-flex justify-content-center align-items-center min-vh-100">
      <Card className="shadow p-4" style={{ width: "30rem" }}>
        <h3 className="text-center mb-4">Register</h3>
        
        {error && <Alert variant="danger">{error}</Alert>}
        {success && <Alert variant="success">{success}</Alert>}
        
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Full Name</Form.Label>
            <Form.Control
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              placeholder="Enter your full name"
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Email</Form.Label>
            <Form.Control
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Password</Form.Label>
            <Form.Control
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter password"
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Confirm Password</Form.Label>
            <Form.Control
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Confirm password"
              required
            />
          </Form.Group>

          <Form.Group className="mb-4">
            <Form.Label>Register as</Form.Label>
            <Form.Select
              name="role"
              value={formData.role}
              onChange={handleChange}
              required
            >
              <option value={USER_ROLES.PATIENT.toUpperCase()}>Patient</option>
              <option value={USER_ROLES.DOCTOR.toUpperCase()}>Doctor</option>
              <option value={USER_ROLES.ADMIN.toUpperCase()}>Admin</option>
            </Form.Select>
            <Form.Text className="text-muted">
              Note: Admin accounts can only be created by existing admins.
            </Form.Text>
          </Form.Group>

          <Button 
            variant="success" 
            type="submit" 
            className="w-100"
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                Creating Account...
              </>
            ) : (
              "Register"
            )}
          </Button>
          
          <p className="text-center mt-3">
            Already have an account? <Link to="/login">Login</Link>
          </p>
        </Form>
      </Card>
    </Container>
  );
};

export default Register;
