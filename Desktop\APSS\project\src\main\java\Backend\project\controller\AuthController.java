package Backend.project.controller;

import Backend.project.config.JwtUtils;
import Backend.project.model.User;
import Backend.project.repositories.UserRepository;
import Backend.project.dtos.loginRequest;
import Backend.project.dtos.RegisterRequest;
import Backend.project.dtos.JwtResponse;
import Backend.project.dtos.ForgotPasswordRequest;
import Backend.project.dtos.ResetPasswordRequest;
import Backend.project.services.PasswordResetService;
import Backend.project.services.UserRegistrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
// import org.springframework.security.crypto.password.PasswordEncoder; // Not used directly in controller
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Optional;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "http://localhost:3000")
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserRepository userRepository;
    
    // PasswordEncoder is used in the services, not directly in the controller
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private PasswordResetService passwordResetService;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody RegisterRequest request) {
        logger.info("Registration attempt for email: {}", request.getEmail());
        
        // Validate request data
        if (request.getEmail() == null || request.getPassword() == null || 
            request.getName() == null || request.getRole() == null) {
            logger.warn("Registration failed: Missing required fields");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("All fields are required");
        }
        
        if (userRepository.findByEmail(request.getEmail()).isPresent()) {
            logger.warn("Registration failed: Email already exists - {}", request.getEmail());
            return ResponseEntity.status(HttpStatus.CONFLICT).body("Email already exists");
        }

        // Normalize role (convert to uppercase)
        String role = request.getRole().toUpperCase();
        // Validate role
        if (!role.equals("PATIENT") && !role.equals("DOCTOR") && !role.equals("ADMIN")) {
            logger.warn("Registration failed: Invalid role - {}", role);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid role. Must be PATIENT, DOCTOR, or ADMIN");
        }

        try {
            // Register user with role-specific data
            User savedUser = userRegistrationService.registerUser(request);
            logger.info("User registered successfully with role {}: {}", role, savedUser.getEmail());
            
            // Return JWT token immediately after registration for better UX
            try {
                Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                        request.getEmail(),
                        request.getPassword()
                    )
                );
                
                SecurityContextHolder.getContext().setAuthentication(authentication);
                String jwt = jwtUtils.generateJwtToken(authentication);
                
                return ResponseEntity.ok(new JwtResponse(
                    jwt,
                    savedUser.getId(),
                    savedUser.getName(),
                    savedUser.getEmail(),
                    savedUser.getRole()
                ));
            } catch (Exception authEx) {
                logger.error("Authentication after registration failed: {}", authEx.getMessage());
                // Even if authentication fails, registration was successful
                return ResponseEntity.ok().body("User registered successfully. Please login.");
            }
        } catch (Exception e) {
            logger.error("Registration failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Registration failed: " + e.getMessage());
        }
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody loginRequest loginRequest) {
        logger.info("Login attempt for email: {}", loginRequest.getEmail());
        
        try {
            // First check if user exists to provide better error messages
            Optional<User> userOpt = userRepository.findByEmail(loginRequest.getEmail());
            if (userOpt.isEmpty()) {
                logger.warn("Login failed: User not found - {}", loginRequest.getEmail());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid email or password");
            }
            
            // Get the user
            User user = userOpt.get();
            logger.debug("Attempting authentication for user: {}", user.getEmail());
            
            // Attempt authentication
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getEmail(),
                            loginRequest.getPassword()
                    )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);
            
            logger.info("User logged in successfully: {}", user.getEmail());
            
            // Create response with user details
            JwtResponse response = new JwtResponse(
                    jwt,
                    user.getId(),
                    user.getName(),
                    user.getEmail(),
                    user.getRole()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Login failed for user {}: {}", loginRequest.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid email or password");
        }
    }
    
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@RequestBody ForgotPasswordRequest request, HttpServletRequest servletRequest) {
        logger.info("Password reset requested for email: {}", request.getEmail());
        
        if (request.getEmail() == null || request.getEmail().isEmpty()) {
            return ResponseEntity.badRequest().body("Email is required");
        }
        
        // Get the base URL for the reset link
        String baseUrl = servletRequest.getScheme() + "://" + servletRequest.getServerName();
        if (servletRequest.getServerPort() != 80 && servletRequest.getServerPort() != 443) {
            baseUrl += ":" + servletRequest.getServerPort();
        }
        
        // For development with React frontend
        baseUrl = "http://localhost:3000";
        
        boolean result = passwordResetService.createPasswordResetTokenForUser(request.getEmail(), baseUrl);
        
        if (result) {
            return ResponseEntity.ok().body("Password reset instructions sent to your email");
        } else {
            // Don't reveal if the email exists or not for security reasons
            return ResponseEntity.ok().body("If your email exists in our system, you will receive reset instructions");
        }
    }
    
    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody ResetPasswordRequest request) {
        logger.info("Password reset attempt with token");
        
        if (request.getToken() == null || request.getPassword() == null) {
            return ResponseEntity.badRequest().body("Token and password are required");
        }
        
        boolean result = passwordResetService.validateTokenAndResetPassword(request.getToken(), request.getPassword());
        
        if (result) {
            return ResponseEntity.ok().body("Password has been reset successfully");
        } else {
            return ResponseEntity.badRequest().body("Invalid or expired token");
        }
    }
}
