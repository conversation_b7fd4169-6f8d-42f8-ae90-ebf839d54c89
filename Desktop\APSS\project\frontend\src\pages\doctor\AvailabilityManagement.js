import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Table, <PERSON><PERSON>, Spin<PERSON>, Card } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCalendarAlt, faClock, faTrash, faPlus } from "@fortawesome/free-solid-svg-icons";
import { API_BASE_URL } from "../../assets/utils/constants";
import axios from "axios";

const AvailabilityManagement = () => {
  const [availabilities, setAvailabilities] = useState([]);
  const [formData, setFormData] = useState({
    dayOfWeek: "MONDAY",
    startTime: "09:00",
    endTime: "17:00"
  });
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  
  // Get user data from localStorage
  const user = JSON.parse(localStorage.getItem("user"));
  const userId = user?.id;
  const token = user?.token;
  
  // State to store the doctor ID
  const [doctorId, setDoctorId] = useState(null);
  const [doctorLoading, setDoctorLoading] = useState(true);
  
  // Check if user has doctor role - log for debugging
  const userRole = user?.role;
  console.log("Current user role:", userRole);
  const isDoctor = userRole === "DOCTOR" || userRole === "doctor";
  
  // Log authentication info for debugging
  useEffect(() => {
    console.log("Authentication info:", {
      isLoggedIn: !!user,
      userId: userId,
      role: userRole,
      isDoctor: isDoctor
    });
  }, [user, userId, userRole, isDoctor]);
  
  // Fetch or create doctor record for this user
  useEffect(() => {
    if (!isDoctor || !userId || !token) return;
    
    const fetchOrCreateDoctor = async () => {
      setDoctorLoading(true);
      try {
        console.log("Fetching or creating doctor record for user ID:", userId);
        const response = await axios.get(
          `${API_BASE_URL}/api/doctors/user/${userId}`,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );
        
        if (response.data && response.data.id) {
          console.log("Doctor record found/created with ID:", response.data.id);
          setDoctorId(response.data.id);
        } else {
          console.error("No doctor ID in response:", response.data);
          setError("Failed to get doctor profile. Please contact an administrator.");
        }
      } catch (err) {
        console.error("Error fetching/creating doctor record:", err);
        const errorMsg = err.response?.data || "Failed to load doctor profile. Please try again later.";
        setError(typeof errorMsg === 'string' ? errorMsg : JSON.stringify(errorMsg));
      } finally {
        setDoctorLoading(false);
      }
    };
    
    fetchOrCreateDoctor();
  }, [userId, isDoctor, token]);
  
  // Fetch doctor's availability slots
  useEffect(() => {
    if (!doctorId || !token || !isDoctor) {
      console.log("Waiting for doctor ID before fetching availability...");
      return;
    }
    
    console.log("Fetching availability for doctor ID:", doctorId);
    let isMounted = true;
    
    const fetchAvailability = async () => {
      setFetchLoading(true);
      try {
        const response = await axios.get(
          `${API_BASE_URL}/api/availability/doctor/${doctorId}`,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );
        console.log("Availability data:", response.data);
        if (isMounted) {
          setAvailabilities(response.data || []);
        }
      } catch (err) {
        console.error("Error fetching availability:", err);
        if (isMounted) {
          const errorMsg = typeof err.response?.data === 'object' ? 
            (err.response?.data?.message || 'Server error') : 
            "Failed to load availability data. Please try again later.";
          setError(errorMsg);
        }
      } finally {
        if (isMounted) {
          setFetchLoading(false);
        }
      }
    };
    
    fetchAvailability();
    
    return () => {
      isMounted = false;
    };
  }, [doctorId, token, isDoctor]);
  
  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Validate time slots
  const validateTimeSlot = () => {
    const startTime = new Date(`2000-01-01T${formData.startTime}`);
    const endTime = new Date(`2000-01-01T${formData.endTime}`);
    
    if (startTime >= endTime) {
      setError("End time must be after start time");
      return false;
    }
    
    // Check for overlapping slots on the same day
    const sameDay = availabilities.filter(slot => slot.dayOfWeek === formData.dayOfWeek);
    
    for (const slot of sameDay) {
      const existingStart = new Date(`2000-01-01T${slot.startTime}`);
      const existingEnd = new Date(`2000-01-01T${slot.endTime}`);
      
      if (
        (startTime >= existingStart && startTime < existingEnd) ||
        (endTime > existingStart && endTime <= existingEnd) ||
        (startTime <= existingStart && endTime >= existingEnd)
      ) {
        setError(`Time slot overlaps with existing slot: ${slot.startTime} - ${slot.endTime}`);
        return false;
      }
    }
    
    return true;
  };
  
  // Add new availability slot
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Check if user is a doctor
    if (!isDoctor) {
      setError("Only doctors can add availability slots");
      return;
    }
    
    // Validate time slot
    if (!validateTimeSlot()) {
      return;
    }
    
    // Check if user is authenticated
    if (!userId) {
      setError("You must be logged in to manage availability slots.");
      return;
    }
    
    setLoading(true);
    setError("");
    setSuccess("");
    
    const addAvailabilitySlot = async () => {
      try {
        // Format the time properly for the backend
        const formattedStartTime = formData.startTime + ":00"; // Add seconds
        const formattedEndTime = formData.endTime + ":00"; // Add seconds
        
        console.log("Sending availability data:", {
          doctorId,
          dayOfWeek: formData.dayOfWeek,
          startTime: formattedStartTime,
          endTime: formattedEndTime
        });
        
        // Check if the server is reachable first
        try {
          await axios.get(`${API_BASE_URL}/api/availability/doctor/${doctorId}`, {
            headers: { Authorization: `Bearer ${token}` },
            timeout: 3000 // 3 second timeout
          });
        } catch (pingErr) {
          if (pingErr.code === 'ECONNREFUSED' || !pingErr.response) {
            throw new Error("Cannot connect to server. Please make sure the backend server is running.");
          }
        }
        
        // Try a different approach - use query parameters in the URL instead of form data
        const url = new URL(`${API_BASE_URL}/api/availability/doctor/${doctorId}`);
        url.searchParams.append("dayOfWeek", formData.dayOfWeek.toUpperCase());
        url.searchParams.append("startTime", formattedStartTime);
        url.searchParams.append("endTime", formattedEndTime);
        
        console.log("Sending request to URL:", url.toString());
        
        // Make the API call with query parameters
        const response = await axios.post(
          url.toString(),
          {}, // Empty body since we're using query parameters
          {
            headers: { 
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json"
            },
            timeout: 10000 // 10 second timeout
          }
        );
        
        console.log("Availability slot created:", response.data);
        
        // Update the UI
        setAvailabilities([...availabilities, response.data]);
        setSuccess("Availability slot added successfully!");
        
        // Reset form times
        setFormData({
          ...formData,
          startTime: "09:00",
          endTime: "17:00"
        });
      } catch (err) {
        console.error("Error adding availability:", err);
        // Safely extract error message - prevent object rendering in React
        let errorMessage = "Failed to add availability slot. Please try again.";
        
        if (err.message === "Cannot connect to server. Please make sure the backend server is running.") {
          errorMessage = err.message;
        } else if (err.code === 'ECONNABORTED') {
          errorMessage = "Request timed out. Please check your connection and try again.";
        } else if (err.response) {
          if (err.response.status === 403) {
            errorMessage = "You don't have permission to add availability slots. Only doctors can manage their availability.";
          } else if (typeof err.response.data === 'string') {
            errorMessage = err.response.data;
          } else if (err.response.data && typeof err.response.data.message === 'string') {
            errorMessage = err.response.data.message;
          } else if (err.response.statusText) {
            errorMessage = `Error: ${err.response.statusText}`;
          }
        } else if (err.request) {
          // The request was made but no response was received
          errorMessage = "No response from server. Please check if the backend is running.";
        }
        
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };
    
    addAvailabilitySlot();
  };
  
  // Delete availability slot
  const handleDelete = async (availabilityId) => {
    if (!window.confirm("Are you sure you want to delete this availability slot?")) {
      return;
    }
    
    // Check if user is a doctor
    if (!isDoctor) {
      setError("Only doctors can delete availability slots");
      return;
    }
    
    setError("");
    setSuccess("");
    
    try {
      await axios.delete(
        `${API_BASE_URL}/api/availability/${availabilityId}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );
      
      setAvailabilities(availabilities.filter(slot => slot.id !== availabilityId));
      setSuccess("Availability slot deleted successfully!");
    } catch (err) {
      console.error("Error deleting availability:", err);
      
      // Safely extract error message
      let errorMessage = "Failed to delete availability slot. Please try again.";
      
      if (err.response) {
        if (err.response.status === 403) {
          errorMessage = "You don't have permission to delete availability slots. Only doctors can manage their availability.";
        } else if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data && typeof err.response.data.message === 'string') {
          errorMessage = err.response.data.message;
        } else if (err.response.statusText) {
          errorMessage = `Error: ${err.response.statusText}`;
        }
      }
      
      setError(errorMessage);
    }
  };
  
  // Format day of week for display
  const formatDayOfWeek = (day) => {
    return day.charAt(0) + day.slice(1).toLowerCase();
  };
  
  // Group availabilities by day
  const groupedAvailabilities = availabilities.reduce((acc, slot) => {
    if (!acc[slot.dayOfWeek]) {
      acc[slot.dayOfWeek] = [];
    }
    acc[slot.dayOfWeek].push(slot);
    return acc;
  }, {});
  
  // Order days of week
  const daysOrder = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
  const sortedDays = Object.keys(groupedAvailabilities).sort(
    (a, b) => daysOrder.indexOf(a) - daysOrder.indexOf(b)
  );
  
  return (
    <Container className="my-5">
      <Row>
        <Col>
          <h2>Manage Your Availability</h2>
          <p className="text-muted">
            Set your weekly availability to let patients know when you're available for appointments.
          </p>
          
          {doctorLoading && (
            <Alert variant="info">
              <Spinner animation="border" size="sm" className="me-2" />
              Loading your doctor profile...
            </Alert>
          )}
        </Col>
      </Row>
      
      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}
      
      <Row className="mb-5">
        <Col lg={6}>
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h4 className="mb-0">Add New Availability Slot</h4>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                    Day of Week
                  </Form.Label>
                  <Form.Select 
                    name="dayOfWeek" 
                    value={formData.dayOfWeek}
                    onChange={handleChange}
                    required
                  >
                    <option value="MONDAY">Monday</option>
                    <option value="TUESDAY">Tuesday</option>
                    <option value="WEDNESDAY">Wednesday</option>
                    <option value="THURSDAY">Thursday</option>
                    <option value="FRIDAY">Friday</option>
                    <option value="SATURDAY">Saturday</option>
                    <option value="SUNDAY">Sunday</option>
                  </Form.Select>
                </Form.Group>
                
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faClock} className="me-2" />
                        Start Time
                      </Form.Label>
                      <Form.Control
                        type="time"
                        name="startTime"
                        value={formData.startTime}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>
                        <FontAwesomeIcon icon={faClock} className="me-2" />
                        End Time
                      </Form.Label>
                      <Form.Control
                        type="time"
                        name="endTime"
                        value={formData.endTime}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                
                <div className="d-grid">
                  <Button 
                    type="submit" 
                    variant="primary" 
                    disabled={loading}
                    className="mt-2"
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Adding...
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faPlus} className="me-2" />
                        Add Availability Slot
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={6} className="mt-4 mt-lg-0">
          <Card className="shadow-sm h-100">
            <Card.Header className="bg-primary text-white">
              <h4 className="mb-0">Availability Guidelines</h4>
            </Card.Header>
            <Card.Body>
              <p>Setting your availability helps patients book appointments during times that work for you.</p>
              <ul>
                <li>Add multiple time slots for each day you're available</li>
                <li>You can set different hours for different days</li>
                <li>Time slots cannot overlap on the same day</li>
                <li>Patients will only be able to book appointments during your available slots</li>
                <li>You can delete a slot if you no longer want to be available at that time</li>
              </ul>
              <Alert variant="info">
                <strong>Tip:</strong> For lunch breaks or other regular pauses, create separate morning and afternoon slots instead of one full-day slot.
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      
      <h3 className="mb-3">
        <FontAwesomeIcon icon={faClock} className="me-2 text-primary" />
        Your Availability Schedule
      </h3>
      
      {fetchLoading ? (
        <div className="text-center py-5">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading your availability schedule...</p>
        </div>
      ) : availabilities.length === 0 ? (
        <Alert variant="info">
          You haven't added any availability slots yet. Add slots above to let patients know when they can book appointments with you.
        </Alert>
      ) : (
        <Row>
          {sortedDays.map(day => (
            <Col md={6} lg={4} key={day} className="mb-4">
              <Card className="shadow-sm h-100">
                <Card.Header className="bg-light">
                  <h5 className="mb-0">{formatDayOfWeek(day)}</h5>
                </Card.Header>
                <Card.Body>
                  <Table responsive borderless className="mb-0">
                    <thead>
                      <tr>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th className="text-end">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {groupedAvailabilities[day].map(slot => (
                        <tr key={slot.id}>
                          <td>{slot.startTime}</td>
                          <td>{slot.endTime}</td>
                          <td className="text-end">
                            <Button 
                              variant="outline-danger" 
                              size="sm"
                              onClick={() => handleDelete(slot.id)}
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </Container>
  );
};

export default AvailabilityManagement;