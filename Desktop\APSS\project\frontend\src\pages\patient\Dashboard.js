import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>er, <PERSON>, <PERSON>, <PERSON>, Button, Badge, Ta<PERSON>, Tab, Alert } from "react-bootstrap";
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faCalendarAlt, 
  faHistory, 
  faBell, 
  faUser, 
  faUserMd, 
  faClinicMedical, 
  faCalendarCheck,
  faCalendarPlus
} from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import { Link, useNavigate } from "react-router-dom";
import { 
  getPatientAppointments, 
  getPatientNotifications, 
  getPatientDashboardStats,
  getAllDoctors,
  cancelAppointment as cancelAppointmentApi, 
  markNotificationAsRead as markNotificationAsReadApi 
} from "../../assets/utils/api";

// Add icons to library
library.add(
  faCalendarAlt, 
  faHistory, 
  faBell, 
  faUser, 
  faUserMd, 
  faClinicMedical, 
  faCalendarCheck,
  faCalendarPlus
);

const PatientDashboard = () => {
  const navigate = useNavigate();
  const [patient, setPatient] = useState({});
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [pastAppointments, setPastAppointments] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [stats, setStats] = useState({
    totalAppointments: 0,
    pendingAppointments: 0,
    completedAppointments: 0,
    canceledAppointments: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [featuredDoctors, setFeaturedDoctors] = useState([]);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (user) {
      setPatient(user);
      setLoading(true);
      setError("");
      
      // Fetch appointments data
      getPatientAppointments(user.id)
        .then(response => {
          const allAppointments = response.data || [];
          console.log('All appointments:', allAppointments);
          
          // Filter for upcoming appointments (today or in the future)
          const now = moment();
          const upcoming = allAppointments.filter(app => 
            moment(app.appointmentDate).isSameOrAfter(now, 'day')
          );
          setUpcomingAppointments(upcoming);
          
          // Filter for past appointments
          const past = allAppointments.filter(app => 
            moment(app.appointmentDate).isBefore(now, 'day')
          );
          setPastAppointments(past);
        })
        .catch(err => {
          console.error("Failed to fetch appointments", err);
          // Continue with other API calls even if this one fails
        });
      
      // Fetch notifications separately
      getPatientNotifications(user.id)
        .then(response => {
          setNotifications(response.data || []);
        })
        .catch(err => {
          console.error("Failed to fetch notifications", err);
          // Use empty array if notifications fail
          setNotifications([]);
        });
      
      // Fetch dashboard stats separately
      try {
        getPatientDashboardStats(user.id)
          .then(response => {
            setStats(response.data || {
              totalAppointments: 0,
              pendingAppointments: 0,
              completedAppointments: 0,
              canceledAppointments: 0
            });
          })
          .catch(err => {
            console.error("Failed to fetch dashboard stats", err);
            // Use default stats if fetch fails
            setStats({
              totalAppointments: 0,
              pendingAppointments: 0,
              completedAppointments: 0,
              canceledAppointments: 0
            });
          });
      } catch (error) {
        console.error("Error in stats fetch", error);
      }
      
      // Fetch doctors separately
      getAllDoctors()
        .then(response => {
          const doctors = response.data || [];
          console.log('Doctors data:', doctors);
          if (doctors.length > 0) {
            // Shuffle array and take first 3 (or fewer if less than 3 available)
            const shuffled = [...doctors].sort(() => 0.5 - Math.random());
            setFeaturedDoctors(shuffled.slice(0, Math.min(3, shuffled.length)));
          } else {
            console.warn('No doctors found in the response');
            setFeaturedDoctors([]);
          }
        })
        .catch(err => {
          console.error("Failed to fetch doctors", err);
          setFeaturedDoctors([]);
          // Set a specific error message for doctors
          setError(prev => prev ? `${prev}\nFailed to load doctors.` : "Failed to load doctors.");
        })
        .finally(() => {
          // Ensure loading is set to false regardless of API success/failure
          setLoading(false);
        });
    }
  }, []);

  const getStatusBadge = (status) => {
    switch(status) {
      case 'PENDING':
        return <Badge bg="warning">Pending</Badge>;
      case 'CONFIRMED':
        return <Badge bg="success">Confirmed</Badge>;
      case 'CANCELED':
        return <Badge bg="danger">Canceled</Badge>;
      case 'COMPLETED':
        return <Badge bg="info">Completed</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  const handleCancelAppointment = (appointmentId) => {
    if (window.confirm("Are you sure you want to cancel this appointment?")) {
      cancelAppointmentApi(appointmentId)
        .then(res => {
          setUpcomingAppointments(upcomingAppointments.map(appt => 
            appt.id === appointmentId ? { ...appt, status: 'CANCELED' } : appt
          ));
          alert("Appointment canceled successfully!");
        })
        .catch(err => {
          console.error("Failed to cancel appointment", err);
          alert("Failed to cancel appointment. Please try again.");
        });
    }
  };

  const handleMarkNotificationAsRead = (notificationId) => {
    markNotificationAsReadApi(notificationId)
      .then(res => {
        setNotifications(notifications.map(notification => 
          notification.id === notificationId ? { ...notification, read: true } : notification
        ));
      })
      .catch(err => {
        console.error("Failed to mark notification as read", err);
      });
  };

  return (
    <Container fluid className="p-4">
      {error && <Alert variant="danger">{error}</Alert>}
      
      <Row className="mb-4">
        <Col>
          <h2>Welcome, {patient.name}</h2>
          <p className="text-muted">Here's your health dashboard</p>
        </Col>
        <Col xs="auto">
          <Link to="/patient/book-appointment">
            <Button variant="primary" size="lg">
              <FontAwesomeIcon icon={faCalendarPlus} className="me-2" />
              Book New Appointment
            </Button>
          </Link>
        </Col>
      </Row>
      
      {/* Stats Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="shadow-sm text-center mb-3 mb-md-0 h-100">
            <Card.Body>
              <div className="d-flex justify-content-center mb-2">
                <div className="rounded-circle bg-primary bg-opacity-10 p-3">
                  <FontAwesomeIcon icon={faCalendarAlt} className="text-primary fa-2x" />
                </div>
              </div>
              <h3 className="mb-1">{stats.totalAppointments || 0}</h3>
              <p className="text-muted mb-0">Total Appointments</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="shadow-sm text-center mb-3 mb-md-0 h-100">
            <Card.Body>
              <div className="d-flex justify-content-center mb-2">
                <div className="rounded-circle bg-warning bg-opacity-10 p-3">
                  <FontAwesomeIcon icon={faCalendarCheck} className="text-warning fa-2x" />
                </div>
              </div>
              <h3 className="mb-1">{stats.pendingAppointments || 0}</h3>
              <p className="text-muted mb-0">Pending Appointments</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="shadow-sm text-center mb-3 mb-md-0 h-100">
            <Card.Body>
              <div className="d-flex justify-content-center mb-2">
                <div className="rounded-circle bg-success bg-opacity-10 p-3">
                  <FontAwesomeIcon icon={faCalendarCheck} className="text-success fa-2x" />
                </div>
              </div>
              <h3 className="mb-1">{stats.completedAppointments || 0}</h3>
              <p className="text-muted mb-0">Completed Appointments</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="shadow-sm text-center mb-3 mb-md-0 h-100">
            <Card.Body>
              <div className="d-flex justify-content-center mb-2">
                <div className="rounded-circle bg-danger bg-opacity-10 p-3">
                  <FontAwesomeIcon icon={faCalendarAlt} className="text-danger fa-2x" />
                </div>
              </div>
              <h3 className="mb-1">{stats.canceledAppointments || 0}</h3>
              <p className="text-muted mb-0">Canceled Appointments</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        {/* Featured Doctors Section */}
        <Col md={4} className="order-md-2">
          <Card className="shadow-sm mb-4">
            <Card.Header className="bg-white">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faUserMd} className="me-2" />
                Featured Doctors
              </h5>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : featuredDoctors.length > 0 ? (
                <div>
                  {featuredDoctors.map(doctor => (
                    <Card key={doctor.id} className="mb-3 border-0 shadow-sm">
                      <Card.Body>
                        <Row>
                          <Col xs={3} className="text-center">
                            <div className="rounded-circle bg-light d-flex align-items-center justify-content-center" style={{width: '60px', height: '60px'}}>
                              <FontAwesomeIcon icon={faUserMd} className="text-primary fa-2x" />
                            </div>
                          </Col>
                          <Col xs={9}>
                            <h6 className="mb-1">{(doctor.role === 'DOCTOR' || doctor.type === 'DOCTOR') ? `Dr. ${doctor.name}` : doctor.name}</h6>
                            <p className="text-muted small mb-2">{doctor.specialty?.name || "General Medicine"}</p>
                            <Link to={`/patient/appointments/book?doctor=${doctor.id}`}>
                              <Button variant="outline-primary" size="sm" className="w-100">
                                Book Appointment
                              </Button>
                            </Link>
                          </Col>
                        </Row>
                      </Card.Body>
                    </Card>
                  ))}
                  <div className="text-center mt-3">
                    <Button 
                      variant="primary" 
                      className="mt-3"
                      onClick={() => navigate('/patient/doctors')}
                    >
                      View All Doctors
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-center py-3">No doctors available at the moment.</p>
              )}
            </Card.Body>
          </Card>
          
          <Card className="shadow-sm">
            <Card.Header className="bg-white">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faBell} className="me-2" />
                Notifications
                {notifications.filter(n => !n.read).length > 0 && (
                  <Badge bg="danger" className="ms-2">
                    {notifications.filter(n => !n.read).length}
                  </Badge>
                )}
              </h5>
            </Card.Header>
            <Card.Body style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : notifications.length > 0 ? (
                notifications.map(notification => (
                  <div 
                    key={notification.id} 
                    className={`p-2 border-bottom ${!notification.read ? 'bg-light' : ''}`}
                    onClick={() => handleMarkNotificationAsRead(notification.id)}
                    style={{ cursor: 'pointer' }}
                  >
                    <p className="mb-1">{notification.message}</p>
                    <small className="text-muted">
                      {moment(notification.dateTimeSent).fromNow()}
                    </small>
                  </div>
                ))
              ) : (
                <p className="text-center py-3">No notifications</p>
              )}
            </Card.Body>
            {notifications.length > 0 && (
              <Card.Footer className="bg-white text-center">
                <Link to="/patient/notifications" className="text-decoration-none">
                  <Button variant="link" className="text-decoration-none">
                    View All Notifications
                  </Button>
                </Link>
              </Card.Footer>
            )}
          </Card>
        </Col>
        
        {/* Appointments Section */}
        <Col md={8} className="order-md-1">
          <Card className="shadow-sm mb-4">
            <Card.Body>
              <Tabs defaultActiveKey="upcoming" className="mb-3">
                <Tab eventKey="upcoming" title="Upcoming Appointments">
                  {upcomingAppointments.length > 0 ? (
                    upcomingAppointments.map(appt => (
                      <Card key={appt.id} className="mb-3">
                        <Card.Body>
                          <Row>
                            <Col md={3} className="text-center border-end">
                              <h5 className="mb-0">{moment(appt.appointmentDate).format('MMM D')}</h5>
                              <p className="mb-0">{moment(appt.appointmentDate).format('h:mm A')}</p>
                              <div className="mt-2">{getStatusBadge(appt.status)}</div>
                            </Col>
                            <Col md={6}>
                              <h5>{(appt.doctorRole === 'DOCTOR' || appt.doctorType === 'DOCTOR') ? `Dr. ${appt.doctorName}` : appt.doctorName}</h5>
                              <p className="text-muted mb-1">{appt.doctorSpecialty}</p>
                          
                              <p className="mb-0"><strong>Reason:</strong> {appt.reason}</p>
                            </Col>
                            <Col md={3} className="d-flex align-items-center justify-content-end">
                              {appt.status !== 'CANCELED' && (
                                <Button 
                                  variant="outline-danger" 
                                  size="sm"
                                  onClick={() => handleCancelAppointment(appt.id)}
                                >
                                  Cancel
                                </Button>
                              )}
                              <Link to={`/patient/appointments/${appt.id}/details`}>
                                <Button 
                                  variant="outline-primary" 
                                  size="sm"
                                  className="ms-2"
                                >
                                  Details
                                </Button>
                              </Link>
                            </Col>
                          </Row>
                        </Card.Body>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-5">
                      <h5>No upcoming appointments</h5>
                      <p className="text-muted">Book an appointment to get started</p>
                      <Button 
                        variant="primary" 
                        onClick={() => navigate('/patient/book-appointment')}
                      >
                        Book Now
                      </Button>
                    </div>
                  )}
                </Tab>
                <Tab eventKey="past" title="Past Appointments">
                  {pastAppointments.length > 0 ? (
                    pastAppointments.map(appt => (
                      <Card key={appt.id} className="mb-3">
                        <Card.Body>
                          <Row>
                            <Col md={3} className="text-center border-end">
                              <h5 className="mb-0">{moment(appt.appointmentDate).format('MMM D')}</h5>
                              <p className="mb-0">{moment(appt.appointmentDate).format('h:mm A')}</p>
                              <div className="mt-2">{getStatusBadge(appt.status)}</div>
                            </Col>
                            <Col md={6}>
                              <h5>{(appt.doctorRole === 'DOCTOR' || appt.doctorType === 'DOCTOR') ? `Dr. ${appt.doctorName}` : appt.doctorName}</h5>
                              <p className="text-muted mb-1">{appt.doctorSpecialty}</p>
                              <p className="mb-0"><strong>Reason:</strong> {appt.reason}</p>
                            </Col>
                            <Col md={3} className="d-flex align-items-center justify-content-end">
                              <Link to={`/patient/appointments/${appt.id}/details`}>
                                <Button 
                                  variant="outline-primary" 
                                  size="sm"
                                >
                                  View Details
                                </Button>
                              </Link>
                            </Col>
                          </Row>
                        </Card.Body>
                      </Card>
                    ))
                  ) : (
                    <p className="text-center py-4">No past appointments found.</p>
                  )}
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={4}>
          <Card className="shadow-sm mb-4">
            <Card.Header className="bg-white">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Your Profile
              </h5>
            </Card.Header>
            <Card.Body>
              {patient && patient.name ? (
                <>
                  <p><strong>Name:</strong> {patient.name}</p>
                  <p><strong>Email:</strong> {patient.email}</p>
                  <p><strong>Phone:</strong> {patient.phone || "Not provided"}</p>
                  <Link to="/patient/profile">
                    <Button variant="outline-primary" className="w-100">
                      Edit Profile
                    </Button>
                  </Link>
                </>
              ) : (
                <div className="text-center py-3">
                  <p className="text-muted">Profile data could not be loaded.</p>
                  <Button 
                    variant="outline-primary" 
                    onClick={() => window.location.reload()}
                    size="sm"
                  >
                    Refresh
                  </Button>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PatientDashboard;