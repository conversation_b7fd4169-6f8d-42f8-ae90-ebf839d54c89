import React from "react";
import "./Table.css";

const AppointmentTable = ({ appointments }) => {
  return (
    <table className="table">
      <thead>
        <tr>
          <th>Patient</th>
          <th>Doctor</th>
          <th>Date</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {appointments.map((appointment) => (
          <tr key={appointment.id}>
            <td>{appointment.patientName}</td>
            <td>{appointment.doctorName}</td>
            <td>{appointment.date}</td>
            <td>{appointment.status}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default AppointmentTable;
