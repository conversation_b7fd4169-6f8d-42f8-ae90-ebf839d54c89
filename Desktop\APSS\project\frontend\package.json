{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^13.5.0", "assert": "^2.1.0", "axios": "^1.8.4", "bootstrap": "^5.3.5", "https-browserify": "^1.0.0", "moment": "^2.30.1", "react": "^17.0.2", "react-big-calendar": "^1.18.0", "react-bootstrap": "^2.10.9", "react-calendar": "^5.1.0", "react-dom": "^17.0.2", "react-router-dom": "^6.30.0", "react-scripts": "^3.0.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.3"}}