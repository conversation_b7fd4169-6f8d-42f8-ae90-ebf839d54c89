import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Container, <PERSON><PERSON>, Spin<PERSON> } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { loginUser } from "../../assets/utils/api";
import { USER_ROLES } from "../../assets/utils/constants";

const LoginForm = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const userData = await loginUser({
        email: formData.email,
        password: formData.password
      });
      
      // Save user data to context
      login(userData);
      
      // Redirect based on user role
      if (userData.role.toUpperCase() === USER_ROLES.ADMIN.toUpperCase()) {
        navigate("/admin/dashboard");
      } else if (userData.role.toUpperCase() === USER_ROLES.DOCTOR.toUpperCase()) {
        navigate("/doctor/dashboard");
      } else if (userData.role.toUpperCase() === USER_ROLES.PATIENT.toUpperCase()) {
        navigate("/patient/dashboard");
      } else {
        navigate("/");
      }
    } catch (err) {
      console.error("Login error:", err);
      if (err.response && err.response.data) {
        setError(err.response.data);
      } else {
        setError("Login failed. Please check your credentials and try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="d-flex justify-content-center align-items-center">
      <Card className="shadow-sm" style={{ maxWidth: "400px", width: "100%" }}>
        <Card.Body className="p-4">
          <h2 className="text-center mb-4">Login</h2>
          
          {error && <Alert variant="danger">{error}</Alert>}
          
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
                required
              />
            </Form.Group>

            <Form.Group className="mb-4">
              <Form.Label>Password</Form.Label>
              <Form.Control
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter your password"
                required
              />
            </Form.Group>

            <Button 
              variant="primary" 
              type="submit" 
              className="w-100"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    aria-hidden="true"
                    className="me-2"
                  />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </Button>
          </Form>
          
          {/* Demo credentials for testing */}
          <div className="mt-4 p-3 bg-light rounded">
            <h6 className="mb-2 text-center">Demo Credentials:</h6>
            <div className="d-flex justify-content-between">
              <div>
                <small className="d-block fw-bold">Patient:</small>
                <small><EMAIL></small>
              </div>
              <div>
                <small className="d-block fw-bold">Doctor:</small>
                <small><EMAIL></small>
              </div>
              <div>
                <small className="d-block fw-bold">Password:</small>
                <small>password</small>
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default LoginForm;
