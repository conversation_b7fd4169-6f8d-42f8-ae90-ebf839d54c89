-- Drop tables if they exist (be careful with this in production)
DROP TABLE IF EXISTS appointment;
DROP TABLE IF EXISTS doctor_availability;
DROP TABLE IF EXISTS notification;
DROP TABLE IF EXISTS doctor;
DROP TABLE IF EXISTS patient;
DROP TABLE IF EXISTS specialty;
DROP TABLE IF EXISTS user;

-- Create user table
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create specialty table
CREATE TABLE IF NOT EXISTS specialty (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create patient table
CREATE TABLE IF NOT EXISTS patient (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    phone VARCHAR(20),
    address TEXT,
    medical_history TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- Create doctor table
CREATE TABLE IF NOT EXISTS doctor (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    specialty_id BIGINT,
    bio TEXT,
    experience VARCHAR(50),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (specialty_id) REFERENCES specialty(id) ON DELETE SET NULL
);

-- Create doctor availability table
CREATE TABLE IF NOT EXISTS doctor_availability (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    doctor_id BIGINT NOT NULL,
    day_of_week VARCHAR(10) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES doctor(id) ON DELETE CASCADE
);

-- Create appointment table
CREATE TABLE IF NOT EXISTS appointment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    patient_id BIGINT NOT NULL,
    doctor_id BIGINT NOT NULL,
    appointment_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctor(id) ON DELETE CASCADE
);

-- Create notification table
CREATE TABLE IF NOT EXISTS notification (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    patient_id BIGINT NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT FALSE,
    date_time_sent TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient(id) ON DELETE CASCADE
);

-- Create payment table
CREATE TABLE IF NOT EXISTS payment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    appointment_id BIGINT NOT NULL,
    patient_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    payment_method VARCHAR(30) NOT NULL,
    transaction_id VARCHAR(50) UNIQUE,
    phone_number VARCHAR(20),
    provider VARCHAR(10),
    payment_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (appointment_id) REFERENCES appointment(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES patient(id) ON DELETE CASCADE
);

-- Insert sample data for testing

-- Insert specialties
INSERT INTO specialty (name, description) VALUES 
('Cardiology', 'Diagnosis and treatment of heart disorders'),
('Dermatology', 'Skin, hair, and nail health'),
('Neurology', 'Disorders of the nervous system'),
('Pediatrics', 'Medical care for infants, children, and adolescents'),
('Orthopedics', 'Musculoskeletal system - bones, joints, ligaments, tendons, muscles'),
('Psychiatry', 'Mental, emotional, and behavioral disorders');

-- Insert sample users (password is 'password' encoded with BCrypt)
INSERT INTO user (name, email, password, role) VALUES 
('Admin User', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'ADMIN'),
('John Smith', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Sarah Johnson', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Michael Brown', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Emily Davis', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Robert Wilson', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Jennifer Lee', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'DOCTOR'),
('Patient User', '<EMAIL>', '$2a$10$ixlPY3AAd4ty1l6E2IsQ9OFZi2ba9ZQE0bP7RFcGIWNhyFrrT3YUi', 'PATIENT');

-- Insert doctors
INSERT INTO doctor (user_id, first_name, last_name, specialty_id, bio, experience, phone, email, address, rating, review_count) VALUES 
(2, 'John', 'Smith', 1, 'Board-certified cardiologist with over 10 years of experience in diagnosing and treating heart conditions.', '10+ years', '************', '<EMAIL>', '123 Medical Center Dr, Suite 101', 4.8, 124),
(3, 'Sarah', 'Johnson', 3, 'Specializing in neurological disorders with a focus on stroke prevention and treatment.', '8+ years', '************', '<EMAIL>', '456 Health Parkway, Suite 202', 4.7, 98),
(4, 'Michael', 'Brown', 4, 'Dedicated pediatrician with a passion for child development and preventive care.', '12+ years', '************', '<EMAIL>', '789 Children\'s Way, Suite 303', 4.9, 156),
(5, 'Emily', 'Davis', 5, 'Orthopedic surgeon specializing in sports medicine and joint replacement.', '15+ years', '************', '<EMAIL>', '321 Sports Medicine Blvd, Suite 404', 4.6, 187),
(6, 'Robert', 'Wilson', 6, 'Compassionate psychiatrist specializing in mood disorders, anxiety, and PTSD treatment.', '11+ years', '************', '<EMAIL>', '678 Mental Health Blvd, Suite 606', 4.7, 163),
(7, 'Jennifer', 'Lee', 2, 'Expert dermatologist with a focus on skin cancer prevention and treatment of complex skin conditions.', '9+ years', '************', '<EMAIL>', '567 Wellness Ave, Suite 505', 4.9, 142);

-- Insert patient
INSERT INTO patient (user_id, first_name, last_name, date_of_birth, gender, phone, address) VALUES 
(8, 'Patient', 'User', '1990-01-15', 'Male', '************', '123 Patient St, Anytown');

-- Insert doctor availability

INSERT INTO doctor_availability (doctor_id, day_of_week, start_time, end_time) VALUES 
(1, 'Monday', '09:00:00', '17:00:00'),
(1, 'Tuesday', '09:00:00', '17:00:00'),
(1, 'Thursday', '09:00:00', '17:00:00'),
(1, 'Friday', '09:00:00', '17:00:00'),
(2, 'Monday', '08:00:00', '16:00:00'),
(2, 'Wednesday', '08:00:00', '16:00:00'),
(2, 'Friday', '08:00:00', '16:00:00'),
(3, 'Tuesday', '09:00:00', '17:00:00'),
(3, 'Wednesday', '09:00:00', '17:00:00'),
(3, 'Thursday', '09:00:00', '17:00:00'),
(3, 'Friday', '09:00:00', '17:00:00'),
(4, 'Monday', '07:00:00', '15:00:00'),
(4, 'Tuesday', '07:00:00', '15:00:00'),
(4, 'Thursday', '07:00:00', '15:00:00'),
(5, 'Tuesday', '10:00:00', '18:00:00'),
(5, 'Thursday', '10:00:00', '18:00:00'),
(5, 'Friday', '10:00:00', '18:00:00'),
(6, 'Monday', '09:00:00', '16:00:00'),
(6, 'Wednesday', '09:00:00', '16:00:00'),
(6, 'Friday', '09:00:00', '16:00:00');