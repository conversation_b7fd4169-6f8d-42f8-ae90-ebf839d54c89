.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #333;
  color: white;
  padding: 15px;
  border-radius: var(--border-radius);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
}

.toast-notification.success {
  background-color: green;
}

.toast-notification.error {
  background-color: red;
}

.toast-notification.warning {
  background-color: orange;
}

.toast-close-btn {
  background: transparent;
  color: white;
  border: none;
  font-size: 16px;
  cursor: pointer;
}
