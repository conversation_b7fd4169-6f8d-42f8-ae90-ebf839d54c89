import React, { useState } from "react";
import Button from "../common/Button";
import "./Form.css";

const AppointmentForm = ({ onSubmit }) => {
  const [patientName, setPatientName] = useState("");
  const [doctorName, setDoctorName] = useState("");
  const [appointmentDate, setAppointmentDate] = useState("");
  const [status, setStatus] = useState("Pending");

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ patientName, doctorName, appointmentDate, status });
  };

  return (
    <form className="form-container" onSubmit={handleSubmit}>
      <h2>Schedule an Appointment</h2>
      <input
        className="input-field"
        type="text"
        placeholder="Patient Name"
        value={patientName}
        onChange={(e) => setPatientName(e.target.value)}
        required
      />
      <input
        className="input-field"
        type="text"
        placeholder="Doctor Name"
        value={doctorName}
        onChange={(e) => setDoctorName(e.target.value)}
        required
      />
      <input
        className="input-field"
        type="datetime-local"
        value={appointmentDate}
        onChange={(e) => setAppointmentDate(e.target.value)}
        required
      />
      <select
        className="input-field"
        value={status}
        onChange={(e) => setStatus(e.target.value)}
        required
      >
        <option value="Pending">Pending</option>
        <option value="Confirmed">Confirmed</option>
        <option value="Completed">Completed</option>
      </select>
      <Button text="Schedule" type="submit" variant="primary" />
    </form>
  );
};

export default AppointmentForm;
