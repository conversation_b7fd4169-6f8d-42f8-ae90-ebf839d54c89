package Backend.project.services;

import Backend.project.model.PasswordResetToken;
import Backend.project.model.User;
import Backend.project.repositories.PasswordResetTokenRepository;
import Backend.project.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
public class PasswordResetService {
    
    private static final Logger logger = LoggerFactory.getLogger(PasswordResetService.class);
    private static final int EXPIRATION_HOURS = 24;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordResetTokenRepository tokenRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private EmailService emailService;
    
    /**
     * Create a password reset token for the user and send a reset email
     */
    @Transactional
    public boolean createPasswordResetTokenForUser(String email, String baseUrl) {
        Optional<User> userOpt = userRepository.findByEmail(email);
        
        if (userOpt.isEmpty()) {
            logger.warn("Password reset requested for non-existent email: {}", email);
            return false;
        }
        
        User user = userOpt.get();
        
        // Remove any existing tokens for this user
        tokenRepository.findByUser(user).ifPresent(token -> tokenRepository.delete(token));
        
        // Create new token
        String token = UUID.randomUUID().toString();
        PasswordResetToken myToken = new PasswordResetToken(
                token,
                user,
                LocalDateTime.now().plusHours(EXPIRATION_HOURS)
        );
        
        tokenRepository.save(myToken);
        logger.info("Created password reset token for user: {}", email);
        
        // Send password reset email
        emailService.sendPasswordResetEmail(user.getEmail(), token, baseUrl);
        logger.info("Password reset email sent to: {}", email);
        
        return true;
    }
    
    /**
     * Validate the token and reset the user's password
     */
    @Transactional
    public boolean validateTokenAndResetPassword(String token, String newPassword) {
        Optional<PasswordResetToken> tokenOpt = tokenRepository.findByToken(token);
        
        if (tokenOpt.isEmpty()) {
            logger.warn("Password reset attempted with invalid token: {}", token);
            return false;
        }
        
        PasswordResetToken resetToken = tokenOpt.get();
        
        // Check if token is expired
        if (resetToken.isExpired()) {
            logger.warn("Password reset attempted with expired token: {}", token);
            tokenRepository.delete(resetToken);
            return false;
        }
        
        // Reset password
        User user = resetToken.getUser();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        // Delete used token
        tokenRepository.delete(resetToken);
        
        logger.info("Password reset successful for user: {}", user.getEmail());
        return true;
    }
}
