package Backend.project.controller;

import Backend.project.dtos.ContactRequest;
import Backend.project.model.Contact;
import Backend.project.repositories.ContactRepository;
import Backend.project.services.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/public")
@CrossOrigin(origins = "http://localhost:3000")
public class ContactController {
    
    private static final Logger logger = LoggerFactory.getLogger(ContactController.class);
    
    @Autowired
    private ContactRepository contactRepository;
    
    @Autowired
    private EmailService emailService;
    
    @Value("${app.admin.email:<EMAIL>}")
    private String adminEmail;
    
    @PostMapping("/contact")
    public ResponseEntity<?> submitContactForm(@RequestBody ContactRequest contactRequest) {
        logger.info("Received contact form submission from: {}", contactRequest.getEmail());
        
        try {
            // Validate request data
            if (contactRequest.getEmail() == null || contactRequest.getName() == null || 
                contactRequest.getMessage() == null) {
                logger.warn("Contact form submission failed: Missing required fields");
                return ResponseEntity.badRequest().body("All fields are required");
            }
            
            // Create new contact message
            Contact contact = new Contact(
                contactRequest.getName(),
                contactRequest.getEmail(),
                contactRequest.getSubject(),
                contactRequest.getMessage()
            );
            
            // Save to database
            Contact savedContact = contactRepository.save(contact);
            logger.info("Contact form submission saved with ID: {}", savedContact.getId());
            
            // Send email notification to admin
            String adminSubject = "New Contact Form Submission: " + contactRequest.getSubject();
            String adminHtmlMessage = String.format(
                "<html><body>" +
                "<h2>New Contact Form Submission</h2>" +
                "<p>A new contact form has been submitted with the following details:</p>" +
                "<table style='border-collapse: collapse; width: 100%; border: 1px solid #ddd;'>" +
                "<tr style='background-color: #f2f2f2;'><th style='padding: 12px; text-align: left; border: 1px solid #ddd;'>Field</th><th style='padding: 12px; text-align: left; border: 1px solid #ddd;'>Value</th></tr>" +
                "<tr><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'><strong>Name</strong></td><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'>%s</td></tr>" +
                "<tr><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'><strong>Email</strong></td><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'>%s</td></tr>" +
                "<tr><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'><strong>Subject</strong></td><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'>%s</td></tr>" +
                "<tr><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'><strong>Message</strong></td><td style='padding: 12px; text-align: left; border: 1px solid #ddd;'>%s</td></tr>" +
                "</table>" +
                "<p style='color: #666; font-size: 12px; margin-top: 20px;'>This message was automatically sent from your MediAppoint system.</p>" +
                "</body></html>",
                contactRequest.getName(),
                contactRequest.getEmail(),
                contactRequest.getSubject(),
                contactRequest.getMessage().replace("\n", "<br/>")
            );
            
            emailService.sendHtmlEmail(adminEmail, adminSubject, adminHtmlMessage);
            logger.info("Admin notification email sent to: {}", adminEmail);
            
            // Send confirmation email to user
            String userSubject = "Thank you for contacting MediAppoint";
            String userHtmlMessage = String.format(
                "<html><body style='font-family: Arial, sans-serif;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;'>" +
                "<div style='background-color: #3498db; padding: 15px; text-align: center; border-radius: 5px 5px 0 0;'>" +
                "<h1 style='color: white; margin: 0;'>MediAppoint</h1>" +
                "</div>" +
                "<div style='padding: 20px;'>" +
                "<p>Dear <strong>%s</strong>,</p>" +
                "<p>Thank you for contacting MediAppoint. We have received your message and will get back to you as soon as possible.</p>" +
                "<p>For your reference, here is a copy of your message:</p>" +
                "<div style='background-color: #f9f9f9; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0;'>" +
                "<p><strong>Subject:</strong> %s</p>" +
                "<p><strong>Message:</strong><br/>%s</p>" +
                "</div>" +
                "<p>If you have any urgent matters, please call our office at <strong>(123) 456-7890</strong>.</p>" +
                "<p>Best regards,<br/>The MediAppoint Team</p>" +
                "</div>" +
                "<div style='background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px; color: #666; border-radius: 0 0 5px 5px;'>" +
                "<p>© 2025 MediAppoint. All rights reserved.</p>" +
                "</div>" +
                "</div>" +
                "</body></html>",
                contactRequest.getName(),
                contactRequest.getSubject(),
                contactRequest.getMessage().replace("\n", "<br/>")
            );
            
            emailService.sendHtmlEmail(contactRequest.getEmail(), userSubject, userHtmlMessage);
            logger.info("Confirmation email sent to user: {}", contactRequest.getEmail());
            
            return ResponseEntity.ok().body("Message sent successfully");
        } catch (Exception e) {
            logger.error("Contact form submission failed: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Failed to send message: " + e.getMessage());
        }
    }
}
