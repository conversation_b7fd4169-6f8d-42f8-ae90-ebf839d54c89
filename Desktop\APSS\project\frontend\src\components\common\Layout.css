.layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  position: relative;
}

.main-content.with-sidebar {
  margin-left: 250px;
  width: calc(100% - 250px);
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
    width: 100%;
  }
  
  .layout {
    flex-direction: column;
  }
}

/* Ensure content doesn't overlap with sidebar */
@media (min-width: 769px) {
  .main-content.with-sidebar {
    padding-left: 30px;
  }
} 