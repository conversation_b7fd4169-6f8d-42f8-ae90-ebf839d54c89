# Server Configuration
server.port=8080

# Database Configuration
spring.application.name=project
spring.datasource.url=************************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate (JPA) Configuration
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Disable automatic schema initialization during startup
spring.sql.init.mode=never
spring.jpa.defer-datasource-initialization=false

# Security (JWT Placeholder for Later)
jwt.secret=intelligentsia_secure_jwt_secret_key_2025
jwt.expiration=86400000

# Logging for debugging authentication
logging.level.org.springframework.security=DEBUG
logging.level.Backend.project=DEBUG

# Email Configuration
# Set to false to use simulation mode (logs emails instead of sending them)
app.email.enabled=false
app.admin.email=<EMAIL>
app.email.from=<EMAIL>

# Gmail SMTP Configuration
# Note: For production, use actual credentials. For development, we're using simulation mode.
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME:<EMAIL>}
spring.mail.password=${EMAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000

# Contact form settings
app.contact.email.subject=New Contact Form Submission
app.contact.email.replySubject=Thank you for contacting us
