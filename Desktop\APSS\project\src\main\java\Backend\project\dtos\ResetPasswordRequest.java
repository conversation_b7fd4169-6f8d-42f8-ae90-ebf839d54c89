package Backend.project.dtos;

public class ResetPasswordRequest {
    private String token;
    private String password;

    // Default constructor
    public ResetPasswordRequest() {
    }

    // Parameterized constructor
    public ResetPasswordRequest(String token, String password) {
        this.token = token;
        this.password = password;
    }

    // Getters and Setters
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
