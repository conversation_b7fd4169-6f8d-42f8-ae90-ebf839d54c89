package Backend.project.dtos;

import Backend.project.model.Doctor;

public class DoctorDTO {
    private Long id;
    private String name;
    private String email;
    private String phone;
    private Long specialtyId;
    // private double rating;

    public DoctorDTO(Doctor doctor) {
        this.id = doctor.getId();
        this.name = doctor.getName();
        this.email = doctor.getEmail();
        this.phone = doctor.getPhone();
       // this.rating = doctor.getRating();
        this.specialtyId = doctor.getSpecialty().getId();// Just include the specialty ID, not the whole object
    }
    
    // Add getters
    public Long getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public Long getSpecialtyId() {
        return specialtyId;
    }
}
