/* BookAppointment.css */

/* Calendar customization */
.react-calendar {
  width: 100%;
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  font-family: inherit;
}

.react-calendar__tile {
  padding: 1rem 0.5rem;
  position: relative;
  text-align: center;
}

.react-calendar__tile--active {
  background: var(--bs-primary) !important;
  color: white;
  border-radius: 0.25rem;
}

.react-calendar__tile--now {
  background: rgba(var(--bs-primary-rgb), 0.1);
  border-radius: 0.25rem;
}

.react-calendar__tile:disabled {
  background-color: #f8f9fa;
  color: #adb5bd;
}

.react-calendar__navigation button {
  font-size: 1.2rem;
  padding: 0.5rem;
}

/* Time slots styling */
.time-slots-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

.time-slot-btn {
  padding: 0.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.time-slot-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Doctor card hover effect */
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border-color: var(--bs-primary);
}

/* Custom tab styling */
.nav-tabs .nav-link {
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  border-bottom: 2px solid transparent;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  color: var(--bs-primary);
  border-bottom: 2px solid var(--bs-primary);
  background-color: transparent;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .time-slots-container {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .react-calendar__tile {
    padding: 0.75rem 0.25rem;
  }
}
