package Backend.project.services;

import Backend.project.dtos.AppointmentDTO;
import Backend.project.model.AppointmentStatus;
import Backend.project.model.Appointment;
import Backend.project.model.Doctor;
import Backend.project.model.Patient;
import Backend.project.repositories.AppointmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AppointmentService {

    private final AppointmentRepository appointmentRepository;
    
    @Autowired
    private DoctorAvailabilityService doctorAvailabilityService;
    
    @Autowired
    private NotificationService notificationService;

    public AppointmentService(AppointmentRepository appointmentRepository) {
        this.appointmentRepository = appointmentRepository;
    }

    public Appointment createAppointment(Appointment appointment) {
        try {
            // Validate appointment data
            if (appointment.getDoctor() == null || appointment.getDoctor().getId() == null) {
                throw new IllegalArgumentException("Doctor information is missing");
            }
            
            if (appointment.getPatient() == null || appointment.getPatient().getId() == null) {
                throw new IllegalArgumentException("Patient information is missing");
            }
            
            if (appointment.getAppointmentDate() == null) {
                throw new IllegalArgumentException("Appointment date is missing");
            }
            
            // Calculate end time based on appointment date and duration
            LocalDateTime startDateTime = appointment.getAppointmentDate();
            LocalTime duration = appointment.getDuration();
            if (duration == null) {
                // Default to 30 minutes if duration is not set
                duration = LocalTime.of(0, 30);
                appointment.setDuration(duration);
            }
            
            LocalDateTime endDateTime = startDateTime.plusMinutes(duration.getHour() * 60 + duration.getMinute());
            
            // Check if the doctor is available at the requested time
            boolean isDoctorAvailable = doctorAvailabilityService.isDoctorAvailable(
                    appointment.getDoctor().getId(),
                    startDateTime.toLocalDate(),
                    startDateTime.toLocalTime(),
                    endDateTime.toLocalTime()
            );
            
            if (!isDoctorAvailable) {
                throw new IllegalStateException("Doctor is not available at the requested time");
            }
            
            // Check for conflicting appointments
            List<Appointment> conflictingAppointments = appointmentRepository.findByDoctorIdAndAppointmentDateBetween(
                    appointment.getDoctor().getId(),
                    startDateTime,
                    endDateTime
            );
            
            if (!conflictingAppointments.isEmpty()) {
                throw new IllegalStateException("There is already an appointment scheduled at this time");
            }
        } catch (Exception e) {
            // Log the error for debugging
            System.err.println("Error validating appointment: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        
        // Set initial status to PENDING
        appointment.setStatus(AppointmentStatus.PENDING);
        
        // Save the appointment
        Appointment savedAppointment = appointmentRepository.save(appointment);
        
        // Send notification to the doctor
        notificationService.sendAppointmentRequestNotification(savedAppointment);
        
        return savedAppointment;
    }
    
    public Appointment modifyAppointment(Appointment appointment) {
        // Check if the appointment exists
        Optional<Appointment> existingAppointment = appointmentRepository.findById(appointment.getId());
        if (existingAppointment.isEmpty()) {
            throw new IllegalArgumentException("Appointment not found");
        }
        
        // If date/time is being modified, check availability
        if (!existingAppointment.get().getAppointmentDate().equals(appointment.getAppointmentDate())) {
            boolean isDoctorAvailable = doctorAvailabilityService.isDoctorAvailable(
                    appointment.getDoctor().getId(),
                    appointment.getAppointmentDate().toLocalDate(),
                    appointment.getAppointmentDate().toLocalTime(),
                    appointment.getEndTime().toLocalTime()
            );
            
            if (!isDoctorAvailable) {
                throw new IllegalStateException("Doctor is not available at the requested time");
            }
            
            // Check for conflicting appointments (excluding this one)
            List<Appointment> conflictingAppointments = appointmentRepository.findByDoctorIdAndAppointmentDateBetweenAndIdNot(
                    appointment.getDoctor().getId(),
                    appointment.getAppointmentDate(),
                    appointment.getEndTime(),
                    appointment.getId()
            );
            
            if (!conflictingAppointments.isEmpty()) {
                throw new IllegalStateException("There is already an appointment scheduled at this time");
            }
        }
        
        // If status is changing, send appropriate notifications
        if (existingAppointment.get().getStatus() != appointment.getStatus()) {
            if (appointment.getStatus() == AppointmentStatus.CONFIRMED) {
                notificationService.sendAppointmentConfirmationNotification(appointment);
            } else if (appointment.getStatus() == AppointmentStatus.CANCELED) {
                notificationService.sendAppointmentCancellationNotification(appointment);
            }
        }
        
        return appointmentRepository.save(appointment);
    }

    public Optional<Appointment> getAppointmentById(Long id) {
        return appointmentRepository.findById(id);
    }

    public List<Appointment> getAppointmentsByPatient(Patient patient) {
        return appointmentRepository.findByPatient(patient);
    }

    public List<Appointment> getAppointmentsByDoctor(Doctor doctor) {
        return appointmentRepository.findByDoctor(doctor);
    }

    public void cancelAppointment(Long id) {
        Optional<Appointment> appointment = appointmentRepository.findById(id);
        if (appointment.isPresent()) {
            Appointment appt = appointment.get();
            appt.setStatus(AppointmentStatus.CANCELED);
            appointmentRepository.save(appt);
            
            // Send cancellation notification
            notificationService.sendAppointmentCancellationNotification(appt);
        } else {
            throw new IllegalArgumentException("Appointment not found with ID: " + id);
        }
    }

    public List<AppointmentDTO> findByDoctorIdAndDate(Long doctorId, LocalDate date) {
        // Find appointments for the given doctor and date
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        
        List<Appointment> appointments = appointmentRepository.findByDoctorIdAndAppointmentDateBetween(
                doctorId, startOfDay, endOfDay);

        return appointments.stream().map(appointment -> new AppointmentDTO(
                appointment.getId(),
                appointment.getAppointmentDate(),
                appointment.getAppointmentDate().toLocalTime(),
                appointment.getPatient().getName(),
                appointment.getReason()
        )).collect(Collectors.toList());
    }
    
    // Get upcoming appointments for a patient
    public List<Appointment> getUpcomingAppointmentsForPatient(Long patientId) {
        LocalDateTime now = LocalDateTime.now();
        return appointmentRepository.findByPatientIdAndAppointmentDateAfterOrderByAppointmentDate(patientId, now);
    }
    
    // Method for controller to call
    public List<Appointment> findByPatientIdAndAppointmentDateAfterOrderByAppointmentDate(Long patientId, LocalDateTime dateTime) {
        return appointmentRepository.findByPatientIdAndAppointmentDateAfterOrderByAppointmentDate(patientId, dateTime);
    }
    
    // Get upcoming appointments for a doctor
    public List<Appointment> getUpcomingAppointmentsForDoctor(Long doctorId) {
        LocalDateTime now = LocalDateTime.now();
        return appointmentRepository.findByDoctorIdAndAppointmentDateAfterOrderByAppointmentDate(doctorId, now);
    }
    
    // Method for controller to call
    public List<Appointment> findByDoctorIdAndAppointmentDateAfterOrderByAppointmentDate(Long doctorId, LocalDateTime dateTime) {
        return appointmentRepository.findByDoctorIdAndAppointmentDateAfterOrderByAppointmentDate(doctorId, dateTime);
    }
    
    // Get appointments that need reminders (24 hours before appointment)
    public List<Appointment> getAppointmentsNeedingReminders() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime reminderWindow = now.plusHours(24);
        
        return appointmentRepository.findByAppointmentDateBetweenAndReminderSentFalseAndStatusNot(
                now, reminderWindow, AppointmentStatus.CANCELED);
    }
    
    // Mark appointment as reminded
    public void markAppointmentAsReminded(Long appointmentId) {
        Optional<Appointment> appointment = appointmentRepository.findById(appointmentId);
        if (appointment.isPresent()) {
            Appointment appt = appointment.get();
            appt.setReminderSent(true);
            appointmentRepository.save(appt);
        }
    }
    
    // Update appointment status
    public Appointment updateAppointmentStatus(Long appointmentId, AppointmentStatus status) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        if (appointmentOpt.isEmpty()) {
            throw new IllegalArgumentException("Appointment not found with ID: " + appointmentId);
        }
        
        Appointment appointment = appointmentOpt.get();
        AppointmentStatus oldStatus = appointment.getStatus();
        appointment.setStatus(status);
        
        // If the appointment is being accepted, send a notification to the patient
        if (oldStatus != status && status == AppointmentStatus.CONFIRMED) {
            notificationService.sendAppointmentConfirmationNotification(appointment);
        }
        
        // If the appointment is being rejected, send a notification to the patient
        if (oldStatus != status && status == AppointmentStatus.CANCELED) {
            notificationService.sendAppointmentCancellationNotification(appointment);
        }
        
        return appointmentRepository.save(appointment);
    }
}

