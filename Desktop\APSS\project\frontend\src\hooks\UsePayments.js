import { useState, useEffect } from "react";
import api from "../assets/utils/api";

const usePayments = (userId) => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPayments = async () => {
      try {
        const response = await api.get(`/payments?userId=${userId}`);
        setPayments(response.data);
      } catch (err) {
        console.error("Error fetching payments:", err);
        setError(err.message);
        // Mock data for demo purposes
        setPayments([
          { id: 1, amount: 150, date: "2023-04-15", status: "PAID", description: "Consultation fee" },
          { id: 2, amount: 75, date: "2023-03-22", status: "PAID", description: "Follow-up appointment" },
          { id: 3, amount: 200, date: "2023-02-10", status: "PAID", description: "Medical tests" }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPayments();
  }, [userId]);

  return { payments, loading, error };
};

export default usePayments;
