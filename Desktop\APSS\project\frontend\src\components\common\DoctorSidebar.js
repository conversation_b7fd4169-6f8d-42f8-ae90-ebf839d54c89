import React from "react";
import { Link, useLocation } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faHome, 
  faUser, 
  faCalendarAlt, 
  faUsers, 
  faPrescription, 
  faChartLine, 
  faDollarSign, 
  faComments, 
  faBell,
  faClock,
  faSignOutAlt
} from "@fortawesome/free-solid-svg-icons";
import "./Sidebar.css";

const DoctorSidebar = ({ onLogout }) => {
  const location = useLocation();
  const user = JSON.parse(localStorage.getItem("user"));

  const menuItems = [
    {
      path: "/doctor/dashboard",
      icon: faHome,
      label: "Dashboard"
    },
    {
      path: "/doctor/profile",
      icon: faUser,
      label: "Profile"
    },
    {
      path: "/doctor/availability",
      icon: faClock,
      label: "Manage Availability"
    },
    {
      path: "/doctor/appointments",
      icon: faCalendarAlt,
      label: "Appointments"
    },
    {
      path: "/doctor/patients",
      icon: faUsers,
      label: "Patients"
    },
    {
      path: "/doctor/prescriptions",
      icon: faPrescription,
      label: "Prescriptions"
    },
    {
      path: "/doctor/reports",
      icon: faChartLine,
      label: "Reports"
    },
    {
      path: "/doctor/earnings",
      icon: faDollarSign,
      label: "Earnings"
    },
    {
      path: "/doctor/messages",
      icon: faComments,
      label: "Messages"
    },
    {
      path: "/doctor/notifications",
      icon: faBell,
      label: "Notifications"
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Doctor Portal</h3>
        <p className="user-info">Dr. {user?.name || 'Doctor'}</p>
      </div>
      
      <nav className="sidebar-nav">
        <ul>
          {menuItems.map((item) => (
            <li key={item.path}>
              <Link 
                to={item.path} 
                className={location.pathname === item.path ? 'active' : ''}
              >
                <FontAwesomeIcon icon={item.icon} className="me-2" />
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className="sidebar-footer">
        <button onClick={onLogout} className="logout-btn">
          <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default DoctorSidebar; 