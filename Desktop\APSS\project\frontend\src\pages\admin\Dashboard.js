import React, { useEffect, useState } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Table, Badge, Form, Modal, Tabs, Tab, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUsers, faUserMd, faUserInjured, faCalendarAlt, faCog, faChartLine, faPlus, faEdit, faTrash, faSearch, faSync } from "@fortawesome/free-solid-svg-icons";
import { useAuth } from "../../context/AuthContext";
import {
  getAdminDashboardStats,
  getAllUsers,
  getAllDoctors,
  getAllPatients,
  getAllAppointments,
  getAllSpecialties,
  createUser,
  updateUser,
  deleteUser,
  createSpecialty,
  updateSpecialty,
  deleteSpecialty
} from "../../assets/utils/api";
import "./Dashboard.css";

const AdminDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("overview");

  // Data states
  const [users, setUsers] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [patients, setPatients] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [specialties, setSpecialties] = useState([]);

  // Modal states
  const [showUserModal, setShowUserModal] = useState(false);
  const [showSpecialtyModal, setShowSpecialtyModal] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [currentSpecialty, setCurrentSpecialty] = useState(null);

  // Form states
  const [userForm, setUserForm] = useState({
    name: "",
    email: "",
    password: "",
    role: "PATIENT"
  });

  const [specialtyForm, setSpecialtyForm] = useState({
    name: "",
    description: ""
  });

  // Stats
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalDoctors: 0,
    totalPatients: 0,
    totalAppointments: 0,
    pendingAppointments: 0,
    completedAppointments: 0,
    canceledAppointments: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  // Function to calculate stats from raw data if API fails
  const calculateStatsFromData = (doctors, patients, users, appointments) => {
    return {
      totalDoctors: doctors.length,
      totalPatients: patients.length,
      totalUsers: users.length,
      totalAppointments: appointments.length,
      pendingAppointments: appointments.filter(a => a.status === "PENDING").length,
      confirmedAppointments: appointments.filter(a => a.status === "CONFIRMED").length,
      completedAppointments: appointments.filter(a => a.status === "COMPLETED").length,
      canceledAppointments: appointments.filter(a => a.status === "CANCELED").length
    };
  };

  const fetchData = async () => {
    setLoading(true);
    setError("");
    
    // Track if we need to calculate stats manually
    let needToCalculateStats = false;
    let doctorsData = [];
    let patientsData = [];
    let usersData = [];
    let appointmentsData = [];

    try {
      // Fetch stats first to check if the admin endpoints are working
      try {
        const statsResponse = await getAdminDashboardStats();
        console.log('Dashboard stats response:', statsResponse);
        if (statsResponse && statsResponse.data) {
          // Verify that the stats data contains actual numbers
          const statsData = statsResponse.data;
          if (statsData.totalDoctors === 0 && statsData.totalPatients === 0 && statsData.totalUsers === 0) {
            console.warn("Stats API returned all zeros, will calculate from raw data");
            needToCalculateStats = true;
          } else {
            setStats(statsData);
          }
        } else {
          needToCalculateStats = true;
        }
      } catch (statsError) {
        console.error("Error fetching dashboard stats:", statsError);
        needToCalculateStats = true;
      }

      // Fetch users
      try {
        const usersResponse = await getAllUsers();
        console.log('Users response:', usersResponse);
        if (usersResponse && usersResponse.data) {
          const userData = Array.isArray(usersResponse.data) ? usersResponse.data : [];
          setUsers(userData);
          usersData = userData;
        }
      } catch (usersError) {
        console.error("Error fetching users:", usersError);
      }

      // Fetch doctors
      try {
        const doctorsResponse = await getAllDoctors();
        console.log('Doctors response:', doctorsResponse);
        if (doctorsResponse && doctorsResponse.data) {
          const doctorData = Array.isArray(doctorsResponse.data) ? doctorsResponse.data : [];
          setDoctors(doctorData);
          doctorsData = doctorData;
        }
      } catch (doctorsError) {
        console.error("Error fetching doctors:", doctorsError);
      }

      // Fetch patients
      try {
        const patientsResponse = await getAllPatients();
        console.log('Patients response:', patientsResponse);
        if (patientsResponse && patientsResponse.data) {
          const patientData = Array.isArray(patientsResponse.data) ? patientsResponse.data : [];
          setPatients(patientData);
          patientsData = patientData;
        }
      } catch (patientsError) {
        console.error("Error fetching patients:", patientsError);
      }

      // Fetch appointments
      try {
        const appointmentsResponse = await getAllAppointments();
        console.log('Appointments response:', appointmentsResponse);
        if (appointmentsResponse && appointmentsResponse.data) {
          const appointmentData = Array.isArray(appointmentsResponse.data) ? appointmentsResponse.data : [];
          setAppointments(appointmentData);
          appointmentsData = appointmentData;
        }
      } catch (appointmentsError) {
        console.error("Error fetching appointments:", appointmentsError);
      }
      
      // If we need to calculate stats manually from the raw data
      if (needToCalculateStats && (doctorsData.length > 0 || patientsData.length > 0 || usersData.length > 0)) {
        console.log("Calculating stats from raw data");
        const calculatedStats = calculateStatsFromData(doctorsData, patientsData, usersData, appointmentsData);
        console.log("Calculated stats:", calculatedStats);
        setStats(calculatedStats);
      }

      // Fetch specialties with fallback mechanism
      try {
        const specialtiesResponse = await getAllSpecialties();
        console.log('Specialties response:', specialtiesResponse);
        if (specialtiesResponse && specialtiesResponse.data) {
          setSpecialties(Array.isArray(specialtiesResponse.data) ? specialtiesResponse.data : []);
        }
      } catch (specialtiesError) {
        console.error("Error fetching specialties:", specialtiesError);
      }

    } catch (err) {
      console.error("Error in fetchData:", err);
      setError("Failed to load data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };


  // ==========================
  // USER FORM HANDLERS
  // ==========================

  // Handle input changes for user form
  const handleUserFormChange = (e) => {
    const { name, value } = e.target;
    setUserForm((prev) => ({ ...prev, [name]: value }));
  };

  // Handle user form submission (Create or Update)
  const handleUserSubmit = async (e) => {
    e.preventDefault();
    setError("");
    
    try {
      console.log('Submitting user form:', userForm);
      
      // Prepare the request data
      const userData = {
        ...userForm,
        // Only include password if it's not empty (for updates)
        password: userForm.password.trim() ? userForm.password : undefined
      };
      
      let response;
      if (currentUser) {
        // Update existing user
        console.log(`Updating user with ID ${currentUser.id}:`, userData);
        response = await updateUser(currentUser.id, userData);
      } else {
        // Create new user
        console.log('Creating new user:', userData);
        response = await createUser(userData);
      }
      
      console.log('User operation response:', response);
      
      // Reset form and close modal
      setUserForm({
        name: "",
        email: "",
        password: "",
        role: "PATIENT"
      });
      setCurrentUser(null);
      setShowUserModal(false);
      
      // Show success message
      alert(currentUser ? "User updated successfully!" : "User created successfully!");
      
      // Refresh data
      fetchData();
    } catch (err) {
      console.error('Error in user operation:', err);
      setError(err.response?.data || err.message || "An error occurred while processing your request");
    }
  };

  // ==========================
  // SPECIALTY FORM HANDLERS
  // ==========================

  // Handle input changes for specialty form
  const handleSpecialtyFormChange = (e) => {
    const { name, value } = e.target;
    setSpecialtyForm((prev) => ({ ...prev, [name]: value }));
  };

  // Handle specialty form submission (Create or Update)
  const handleSpecialtySubmit = async (e) => {
    e.preventDefault();
    setError("");

    try {
      console.log('Submitting specialty form:', specialtyForm);
      
      let response;
      if (currentSpecialty) {
        // Update existing specialty
        console.log(`Updating specialty with ID ${currentSpecialty.id}:`, specialtyForm);
        response = await updateSpecialty(currentSpecialty.id, specialtyForm);
      } else {
        // Create new specialty
        console.log('Creating new specialty:', specialtyForm);
        response = await createSpecialty(specialtyForm);
      }
      
      console.log('Specialty operation response:', response);

      // Reset form and close modal
      setSpecialtyForm({
        name: "",
        description: ""
      });
      setCurrentSpecialty(null);
      setShowSpecialtyModal(false);
      
      // Show success message
      alert(currentSpecialty ? "Specialty updated successfully!" : "Specialty created successfully!");

      // Refresh data
      fetchData();
    } catch (err) {
      console.error('Error in specialty operation:', err);
      setError(err.response?.data || err.message || "An error occurred while processing your request");
    }
  };

  // ==========================
  // EDIT HANDLERS
  // ==========================

  const handleEditUser = (user) => {
    setCurrentUser(user);
    setUserForm({
      name: user.name,
      email: user.email,
      password: "", // Clear password field for security
      role: user.role,
    });
    setShowUserModal(true);
  };

  const handleEditSpecialty = (specialty) => {
    setCurrentSpecialty(specialty);
    setSpecialtyForm({
      name: specialty.name,
      description: specialty.description,
    });
    setShowSpecialtyModal(true);
  };

  // ==========================
  // DELETE HANDLERS
  // ==========================

  const handleDeleteUser = async (userId) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      try {
        await deleteUser(userId);
        // Refresh data
        fetchData();
      } catch (err) {
        setError(err.response?.data?.message || err.message);
      }
    }
  };

  const handleDeleteSpecialty = async (specialtyId) => {
    if (window.confirm("Are you sure you want to delete this specialty?")) {
      try {
        await deleteSpecialty(specialtyId);
        // Refresh data
        fetchData();
      } catch (err) {
        setError(err.response?.data?.message || err.message);
      }
    }
  };

  // ==========================
  // LOADING STATE
  // ==========================

  if (loading) {
    return (
      <Container
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "80vh" }}
      >
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading dashboard...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="admin-dashboard py-4">
      {error && (
        <Alert variant="danger" className="mb-4">
          <Alert.Heading>Error Loading Dashboard Data</Alert.Heading>
          <p>{error}</p>
          <div className="d-flex justify-content-end">
            <Button onClick={fetchData} variant="outline-danger">
              <FontAwesomeIcon icon={faSync} className="me-2" />
              Refresh Data
            </Button>
          </div>
        </Alert>
      )}

      <Row className="mb-4">
        <Col md={8}>
          <h2 className="welcome-heading">
            <span className="text-primary">Welcome, Admin {user?.name}</span>
          </h2>
          <p className="text-muted">Manage your clinic's appointments, doctors, patients, and more.</p>
        </Col>
        <Col md={4} className="d-flex justify-content-end align-items-center">
          <Button variant="outline-primary" onClick={fetchData} className="refresh-btn">
            <FontAwesomeIcon icon={faSync} className="me-2" />
            Refresh Dashboard
          </Button>
        </Col>
      </Row>

      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4 admin-tabs"
      >
        <Tab eventKey="overview" title={<span><FontAwesomeIcon icon={faChartLine} className="me-2" />Overview</span>}>
          <Row>
            <Col md={3} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Body className="text-center">
                  <div className="stat-icon bg-primary">
                    <FontAwesomeIcon icon={faUserMd} size="2x" />
                  </div>
                  <h3 className="mt-3">{stats.totalDoctors}</h3>
                  <p className="text-muted mb-0">Doctors</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Body className="text-center">
                  <div className="stat-icon bg-success">
                    <FontAwesomeIcon icon={faUserInjured} size="2x" />
                  </div>
                  <h3 className="mt-3">{stats.totalPatients}</h3>
                  <p className="text-muted mb-0">Patients</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Body className="text-center">
                  <div className="stat-icon bg-info">
                    <FontAwesomeIcon icon={faCalendarAlt} size="2x" />
                  </div>
                  <h3 className="mt-3">{stats.totalAppointments}</h3>
                  <p className="text-muted mb-0">Appointments</p>
                </Card.Body>
              </Card>
            </Col>
            <Col md={3} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Body className="text-center">
                  <div className="stat-icon bg-warning">
                    <FontAwesomeIcon icon={faUsers} size="2x" />
                  </div>
                  <h3 className="mt-3">{stats.totalUsers}</h3>
                  <p className="text-muted mb-0">Total Users</p>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <Row>
            <Col md={6} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Header className="bg-white">
                  <h5 className="mb-0">Recent Appointments</h5>
                </Card.Header>
                <Card.Body>
                  <Table responsive hover className="mb-0">
                    <thead>
                      <tr>
                        <th>Patient</th>
                        <th>Doctor</th>
                        <th>Date</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {appointments.length > 0 ? (
                        appointments.slice(0, 5).map((appointment) => (
                          <tr key={appointment.id}>
                            <td>{appointment.patient?.name || appointment.patientName || 'Unknown Patient'}</td>
                            <td>{appointment.doctor?.name || appointment.doctorName || 'Unknown Doctor'}</td>
                            <td>
                              {appointment.appointmentDate ? new Date(appointment.appointmentDate).toLocaleDateString() : 'No date'}
                              {appointment.appointmentTime && ` ${appointment.appointmentTime}`}
                            </td>
                            <td>
                              <Badge bg={
                                appointment.status === "CONFIRMED" ? "success" :
                                appointment.status === "PENDING" ? "warning" :
                                appointment.status === "CANCELED" ? "danger" :
                                appointment.status === "COMPLETED" ? "info" : "secondary"
                              }>
                                {appointment.status || 'Unknown'}
                              </Badge>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="4" className="text-center py-3">No appointments found</td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>

            <Col md={6} className="mb-4">
              <Card className="dashboard-card h-100">
                <Card.Header className="bg-white">
                  <h5 className="mb-0">Top Doctors</h5>
                </Card.Header>
                <Card.Body>
                  <Table responsive hover className="mb-0">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Specialty</th>
                        <th>Patients</th>
                        <th>Rating</th>
                      </tr>
                    </thead>
                    <tbody>
                      {doctors.length > 0 ? (
                        doctors.slice(0, 5).map((doctor) => (
                          <tr key={doctor.id}>
                            <td>{doctor.name || doctor.user?.name || 'Unknown'}</td>
                            <td>{doctor.specialty?.name || doctor.specialtyName || 'General'}</td>
                            <td>{doctor.patientCount || '0'}</td>
                            <td>{doctor.rating || '4'}/5</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="4" className="text-center py-3">No doctors found</td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        <Tab eventKey="users" title={<span><FontAwesomeIcon icon={faUsers} className="me-2" />Users</span>}>
          <Card className="dashboard-card">
            <Card.Header className="bg-white d-flex justify-content-between align-items-center">
              <h5 className="mb-0">User Management</h5>
              <Button
                variant="primary"
                size="sm"
                onClick={() => {
                  setCurrentUser(null);
                  setUserForm({ name: "", email: "", password: "", role: "PATIENT" });
                  setShowUserModal(true);
                }}
              >
                <FontAwesomeIcon icon={faPlus} className="me-2" />
                Add User
              </Button>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <Form.Control
                  type="text"
                  placeholder="Search users..."
                  className="search-input"
                />
              </div>
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td>{user.id}</td>
                      <td>{user.name}</td>
                      <td>{user.email}</td>
                      <td>
                        <Badge bg={
                          user.role === "ADMIN" ? "danger" :
                          user.role === "DOCTOR" ? "primary" :
                          "success"
                        }>
                          {user.role}
                        </Badge>
                      </td>
                      <td>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          className="me-2"
                          onClick={() => handleEditUser(user)}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="doctors" title={<span><FontAwesomeIcon icon={faUserMd} className="me-2" />Doctors</span>}>
          <Card className="dashboard-card">
            <Card.Header className="bg-white">
              <h5 className="mb-0">Doctor Management</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <Form.Control
                  type="text"
                  placeholder="Search doctors..."
                  className="search-input"
                />
              </div>
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Specialty</th>
                    <th>Patients</th>
                    <th>Rating</th>
                  </tr>
                </thead>
                <tbody>
                  {doctors.map((doctor) => (
                    <tr key={doctor.id}>
                      <td>{doctor.id}</td>
                      <td>{doctor.name}</td>
                      <td>{doctor.specialty}</td>
                      <td>{doctor.patients}</td>
                      <td>{doctor.rating}/5</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="appointments" title={<span><FontAwesomeIcon icon={faCalendarAlt} className="me-2" />Appointments</span>}>
          <Card className="dashboard-card">
            <Card.Header className="bg-white">
              <h5 className="mb-0">Appointment Management</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <Form.Control
                  type="text"
                  placeholder="Search appointments..."
                  className="search-input"
                />
              </div>
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Patient</th>
                    <th>Doctor</th>
                    <th>Date & Time</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {appointments.map((appointment) => (
                    <tr key={appointment.id}>
                      <td>{appointment.id}</td>
                      <td>{appointment.patient}</td>
                      <td>{appointment.doctor}</td>
                      <td>{appointment.date} {appointment.time}</td>
                      <td>
                        <Badge bg={
                          appointment.status === "CONFIRMED" ? "success" :
                          appointment.status === "PENDING" ? "warning" :
                          appointment.status === "CANCELED" ? "danger" : "secondary"
                        }>
                          {appointment.status}
                        </Badge>
                      </td>
                      <td>
                        <Button variant="outline-primary" size="sm" className="me-2">
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button variant="outline-danger" size="sm">
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="specialties" title={<span><FontAwesomeIcon icon={faCog} className="me-2" />Specialties</span>}>
          <Card className="dashboard-card">
            <Card.Header className="bg-white d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Specialty Management</h5>
              <Button
                variant="primary"
                size="sm"
                onClick={() => {
                  setCurrentSpecialty(null);
                  setSpecialtyForm({ name: "", description: "" });
                  setShowSpecialtyModal(true);
                }}
              >
                <FontAwesomeIcon icon={faPlus} className="me-2" />
                Add Specialty
              </Button>
            </Card.Header>
            <Card.Body>
              <Table responsive hover>
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {specialties.map((specialty) => (
                    <tr key={specialty.id}>
                      <td>{specialty.id}</td>
                      <td>{specialty.name}</td>
                      <td>{specialty.description}</td>
                      <td>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          className="me-2"
                          onClick={() => handleEditSpecialty(specialty)}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDeleteSpecialty(specialty.id)}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>

      {/* User Modal */}
      <Modal show={showUserModal} onHide={() => setShowUserModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{currentUser ? "Edit User" : "Add New User"}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleUserSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={userForm.name}
                onChange={handleUserFormChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={userForm.email}
                onChange={handleUserFormChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Password {currentUser && "(leave blank to keep current)"}</Form.Label>
              <Form.Control
                type="password"
                name="password"
                value={userForm.password}
                onChange={handleUserFormChange}
                required={!currentUser}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Role</Form.Label>
              <Form.Select
                name="role"
                value={userForm.role}
                onChange={handleUserFormChange}
                required
              >
                <option value="PATIENT">Patient</option>
                <option value="DOCTOR">Doctor</option>
                <option value="ADMIN">Admin</option>
              </Form.Select>
            </Form.Group>
            <div className="d-flex justify-content-end">
              <Button variant="secondary" className="me-2" onClick={() => setShowUserModal(false)}>
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                {currentUser ? "Update" : "Create"}
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>

      {/* Specialty Modal */}
      <Modal show={showSpecialtyModal} onHide={() => setShowSpecialtyModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{currentSpecialty ? "Edit Specialty" : "Add New Specialty"}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSpecialtySubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={specialtyForm.name}
                onChange={handleSpecialtyFormChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={specialtyForm.description}
                onChange={handleSpecialtyFormChange}
                required
              />
            </Form.Group>
            <div className="d-flex justify-content-end">
              <Button variant="secondary" className="me-2" onClick={() => setShowSpecialtyModal(false)}>
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                {currentSpecialty ? "Update" : "Create"}
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default AdminDashboard;
