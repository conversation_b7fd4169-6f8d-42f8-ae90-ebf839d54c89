import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faHistory, faCalendarAlt, faUserMd, faClock, faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

const AppointmentHistory = () => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchAppointmentHistory();
  }, []);

  const fetchAppointmentHistory = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/appointments/patient/${user.id}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      setAppointments(response.data || []);
    } catch (err) {
      console.error("Error fetching appointment history:", err);
      setError("Failed to load appointment history");
      // Mock data for demo
      setAppointments([
        {
          id: 1,
          appointmentDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Smith",
          doctorSpecialty: "Cardiology",
          status: "COMPLETED",
          reason: "Heart checkup",
          amount: 10000
        },
        {
          id: 2,
          appointmentDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Johnson",
          doctorSpecialty: "Neurology",
          status: "CANCELED",
          reason: "Headache consultation",
          amount: 10000
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "COMPLETED":
        return <Badge bg="success">Completed</Badge>;
      case "CONFIRMED":
        return <Badge bg="primary">Confirmed</Badge>;
      case "PENDING":
        return <Badge bg="warning">Pending</Badge>;
      case "CANCELED":
        return <Badge bg="danger">Canceled</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading appointment history...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FontAwesomeIcon icon={faHistory} className="me-2" />
            Appointment History
          </h2>
          <p className="text-muted">View all your past appointments and their details</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Card className="shadow-sm">
        <Card.Header>
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
            Past Appointments
          </h5>
        </Card.Header>
        <Card.Body>
          {appointments.length > 0 ? (
            <Table responsive>
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Doctor</th>
                  <th>Specialty</th>
                  <th>Reason</th>
                  <th>Status</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                {appointments.map((appointment) => (
                  <tr key={appointment.id}>
                    <td>
                      <div>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-primary" />
                        {moment(appointment.appointmentDate).format("MMM D, YYYY")}
                      </div>
                      <div>
                        <FontAwesomeIcon icon={faClock} className="me-2 text-muted" />
                        {moment(appointment.appointmentDate).format("h:mm A")}
                      </div>
                    </td>
                    <td>
                      <div>
                        <FontAwesomeIcon icon={faUserMd} className="me-2 text-info" />
                        {appointment.doctorName}
                      </div>
                    </td>
                    <td>{appointment.doctorSpecialty}</td>
                    <td>{appointment.reason}</td>
                    <td>{getStatusBadge(appointment.status)}</td>
                    <td>
                      {appointment.amount ? (
                        <span className="fw-bold">{appointment.amount.toLocaleString()} XAF</span>
                      ) : (
                        <span className="text-muted">Not specified</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-5">
              <FontAwesomeIcon icon={faHistory} className="text-muted fa-3x mb-3" />
              <h5>No appointment history</h5>
              <p className="text-muted">Your past appointments will appear here</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AppointmentHistory;
