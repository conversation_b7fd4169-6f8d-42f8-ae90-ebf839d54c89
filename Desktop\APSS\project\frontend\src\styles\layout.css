/* Flexbox Layout */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Grid Layout */
.grid {
  display: grid;
  gap: var(--spacing-medium);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Sidebar Layout */
.sidebar {
  width: 250px;
  background: var(--primary-color);
  color: white;
  padding: 20px;
}

.content {
  flex-grow: 1;
  padding: 20px;
}
