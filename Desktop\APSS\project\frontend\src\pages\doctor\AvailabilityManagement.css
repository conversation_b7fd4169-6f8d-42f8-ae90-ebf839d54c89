.availability-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.availability-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.day-header {
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.time-slot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.time-slot:last-child {
  border-bottom: none;
}

.time-display {
  display: flex;
  align-items: center;
}

.time-icon {
  margin-right: 8px;
  color: #6c757d;
}

.delete-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.delete-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.form-container {
  border-radius: 10px;
  overflow: hidden;
}

.form-header {
  padding: 15px;
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  color: white;
}

.guidelines-container {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
}

.guidelines-list li {
  margin-bottom: 10px;
}

.guidelines-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  margin-top: 20px;
}

.empty-state-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 15px;
}
