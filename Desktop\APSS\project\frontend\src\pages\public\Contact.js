import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Spin<PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faPhone, faMapMarkerAlt, faUser, faPaperPlane } from "@fortawesome/free-solid-svg-icons";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import "./Contact.css";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState("");
  const [error, setError] = useState("");

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setSuccess("");
    setError("");
    
    try {
      // Send the contact form data to the backend API
      const response = await axios.post(`${API_BASE_URL}/api/public/contact-us`, formData);
      console.log('Contact form submission response:', response.data);
      
      // Show success message
      setSuccess("Your message has been sent successfully! Our team will get back to you soon.");
      
      // Clear the form
      setFormData({ name: "", email: "", subject: "", message: "" });
    } catch (err) {
      console.error("Error sending contact form:", err);
      setError("Failed to send message. Please try again later.");
      
      // Fallback for development - simulate successful submission
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          setSuccess("Development mode: Message simulated as sent successfully!");
          setFormData({ name: "", email: "", subject: "", message: "" });
          setError("");
        }, 1000);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="py-5 contact-container">
      <Row className="justify-content-center mb-5">
        <Col md={8} className="text-center">
          <h1 className="display-4 mb-4">Contact Us</h1>
          <p className="lead text-muted">
            Have questions or need assistance? We're here to help! Fill out the form below and we'll get back to you as soon as possible.
          </p>
        </Col>
      </Row>
      
      <Row className="justify-content-center">
        <Col lg={5} md={6} className="mb-4 mb-md-0">
          <Card className="shadow-sm h-100">
            <Card.Body>
              <h3 className="mb-4">Send Us a Message</h3>
              
              {success && <Alert variant="success">{success}</Alert>}
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    <FontAwesomeIcon icon={faUser} className="me-2 text-primary" />
                    Your Name
                  </Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter your full name"
                    required
                  />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>
                    <FontAwesomeIcon icon={faEnvelope} className="me-2 text-primary" />
                    Email Address
                  </Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    required
                  />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Subject</Form.Label>
                  <Form.Control
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    placeholder="What is this regarding?"
                    required
                  />
                </Form.Group>
                
                <Form.Group className="mb-4">
                  <Form.Label>Message</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={5}
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Please provide details about your inquiry"
                    required
                  />
                </Form.Group>
                
                <div className="d-grid">
                  <Button 
                    variant="primary" 
                    type="submit"
                    className="py-2"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Sending...
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faPaperPlane} className="me-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={4} md={6}>
          <Card className="shadow-sm h-100">
            <Card.Body>
              <h3 className="mb-4">Contact Information</h3>
              
              <div className="contact-info">
                <div className="mb-4">
                  <h5>
                    <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2 text-primary" />
                    Address
                  </h5>
                  <p className="text-muted mb-0">123 Medical Center Drive</p>
                  <p className="text-muted">New York, NY 10001</p>
                </div>
                
                <div className="mb-4">
                  <h5>
                    <FontAwesomeIcon icon={faPhone} className="me-2 text-primary" />
                    Phone
                  </h5>
                  <p className="text-muted">(*************</p>
                </div>
                
                <div className="mb-4">
                  <h5>
                    <FontAwesomeIcon icon={faEnvelope} className="me-2 text-primary" />
                    Email
                  </h5>
                  <p className="text-muted"><EMAIL></p>
                </div>
                
                <div>
                  <h5>Hours of Operation</h5>
                  <p className="text-muted mb-1">Monday - Friday: 8:00 AM - 8:00 PM</p>
                  <p className="text-muted mb-1">Saturday: 9:00 AM - 5:00 PM</p>
                  <p className="text-muted">Sunday: Closed</p>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
      
      <Row className="justify-content-center mt-5">
        <Col md={10}>
          <div className="map-container">
            <iframe 
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d193595.15830869428!2d-74.11976397304903!3d40.69766374874431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1652890841065!5m2!1sen!2sca" 
              width="100%" 
              height="450" 
              style={{ border: 0, borderRadius: '8px' }} 
              allowFullScreen="" 
              loading="lazy" 
              referrerPolicy="no-referrer-when-downgrade"
              title="Our Location"
            ></iframe>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default Contact;
