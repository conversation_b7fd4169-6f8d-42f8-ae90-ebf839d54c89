package Backend.project.services;

import Backend.project.model.User;
import Backend.project.model.Patient;
import Backend.project.model.Doctor;
import Backend.project.model.Admin;
import Backend.project.model.Specialty;
import Backend.project.model.Role;
import Backend.project.repositories.UserRepository;
import Backend.project.repositories.PatientRepository;
import Backend.project.repositories.DoctorRepository;
import Backend.project.repositories.AdminRepository;
import Backend.project.repositories.SpecialtyRepository;
import Backend.project.dtos.RegisterRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class UserRegistrationService {
    private static final Logger logger = LoggerFactory.getLogger(UserRegistrationService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private SpecialtyRepository specialtyRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Transactional
    public User registerUser(RegisterRequest request) {
        logger.info("Starting registration for user with email: {} and role: {}", request.getEmail(), request.getRole());
        
        // Create base user
        User user = new User(
            request.getName(),
            request.getEmail(),
            passwordEncoder.encode(request.getPassword()),
            request.getRole().toUpperCase()
        );

        // Save the user first to get the ID
        user = userRepository.save(user);
        logger.info("Created base user with ID: {}", user.getId());

        // Create role-specific entry
        String role = user.getRole().toUpperCase();
        switch (role) {
            case "PATIENT":
                Patient patient = new Patient();
                patient.setUser(user);
                patient.setName(user.getName());
                patient.setEmail(user.getEmail());
                patient.setRole(Role.PATIENT);
                patientRepository.save(patient);
                logger.info("Created patient entry for user ID: {}", user.getId());
                break;

            case "DOCTOR":
                Doctor doctor = new Doctor();
                doctor.setUser(user);
                doctor.setName(user.getName());
                doctor.setEmail(user.getEmail());
                doctor.setRole(Role.DOCTOR);
                
                // Set default values for doctor-specific fields
                if (request.getSpecialtyId() != null) {
                    try {
                        Specialty specialty = specialtyRepository.findById(request.getSpecialtyId())
                            .orElseThrow(() -> new IllegalArgumentException("Specialty not found"));
                        doctor.setSpecialty(specialty);
                    } catch (Exception e) {
                        logger.error("Error setting specialty for doctor: {}", e.getMessage());
                        // Continue with registration even if specialty is not found
                    }
                }
                
                if (request.getLicenseNumber() != null) {
                    doctor.setLicenseNumber(request.getLicenseNumber());
                }
                
                doctorRepository.save(doctor);
                logger.info("Created doctor entry for user ID: {}", user.getId());
                break;

            case "ADMIN":
                Admin admin = new Admin();
                admin.setUser(user);
                admin.setName(user.getName());
                admin.setEmail(user.getEmail());
                adminRepository.save(admin);
                logger.info("Created admin entry for user ID: {}", user.getId());
                break;

            default:
                logger.error("Invalid role encountered during registration: {}", role);
                throw new IllegalArgumentException("Invalid role: " + role);
        }

        return user;
    }
}
