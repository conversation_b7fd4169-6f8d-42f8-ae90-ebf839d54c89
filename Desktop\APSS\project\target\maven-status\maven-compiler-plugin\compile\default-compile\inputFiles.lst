C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\config\CORSConfig.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\config\DataInitializer.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\config\JwtAuthenticationFilter.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\config\JwtUtils.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\config\SecurityConfig.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\AdminController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\AppointmentController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\AuthController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\ContactController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\DashboardController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\DoctorAvailabilityController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\DoctorController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\NotificationController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\PatientController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\PaymentController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\PublicController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\SpecialtyController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\controller\UserController.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\AppointmentDTO.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\ContactRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\DoctorDTO.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\ForgotPasswordRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\JwtResponse.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\loginRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\Loginresponse.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\RegisterRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\dtos\ResetPasswordRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Admin.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Appointment.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\AppointmentStatus.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Contact.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Doctor.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\DoctorAvailability.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Notification.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\PasswordResetToken.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Patient.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Payment.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\PaymentMethod.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\PaymentStatus.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Role.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\Specialty.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\model\User.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\payload\request\SpecialtyRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\payload\request\UserRequest.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\ProjectApplication.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\AdminRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\AppointmentRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\ContactRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\DoctorAvailabilityRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\DoctorRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\NotificationRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\PasswordResetTokenRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\PatientRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\PaymentRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\SpecialtyRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\repositories\UserRepository.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\AppointmentService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\DoctorAvailabilityService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\DoctorService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\EmailService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\NotificationService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\PasswordResetService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\PatientService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\PaymentService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\SpecialtyService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\UserDetailsServiceImpl.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\UserRegistrationService.java
C:\Users\<USER>\Desktop\APSS\project\src\main\java\Backend\project\services\UserService.java
