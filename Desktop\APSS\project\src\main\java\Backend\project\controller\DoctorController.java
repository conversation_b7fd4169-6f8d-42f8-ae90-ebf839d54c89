package Backend.project.controller;

import Backend.project.model.Doctor;
import Backend.project.model.DoctorAvailability;
import Backend.project.model.User;
import Backend.project.model.Appointment;
import Backend.project.repositories.DoctorRepository;
import Backend.project.repositories.UserRepository;
import Backend.project.services.DoctorAvailabilityService;
import Backend.project.services.DoctorService;
import Backend.project.services.AppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/doctors")
@CrossOrigin(origins = "http://localhost:3000")  // Enable CORS globally for this controller
public class DoctorController {

    private final DoctorService doctorService;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private DoctorAvailabilityService availabilityService;

    @Autowired
    private AppointmentService appointmentService;

    public DoctorController(DoctorService doctorService) {
        this.doctorService = doctorService;
    }

    @PostMapping
    public Doctor createDoctor(@RequestBody Doctor doctor) {
        return doctorService.createDoctor(doctor);
    }

    @GetMapping("/{id}")
    public Optional<Doctor> getDoctorById(@PathVariable Long id) {
        return doctorService.getDoctorById(id);
    }
    @GetMapping("/doctors/{id}/profile")
    public ResponseEntity<Doctor> getDoctorProfile(@PathVariable Long id) {
        return doctorService.getDoctorProfile(id);
    }

    @GetMapping
    public List<Doctor> getAllDoctors() {
        return doctorService.getAllDoctors();
    }

    @GetMapping("/specialty/{specialtyId}")
    public List<Doctor> getDoctorsBySpecialty(@PathVariable Long specialtyId) {
        return doctorService.getDoctorsBySpecialty(specialtyId);
    }

    /**
     * Get or create a doctor record for a user
     * @param userId The user ID
     * @return The doctor record
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('DOCTOR') or hasAuthority('ADMIN')")
    public ResponseEntity<?> getDoctorByUserId(@PathVariable Long userId) {
        try {
            // Check if a doctor record exists for this user
            var doctorOpt = doctorRepository.findByUserId(userId);
            
            if (doctorOpt.isPresent()) {
                // Return the existing doctor record
                return ResponseEntity.ok(doctorOpt.get());
            } else {
                // Find the user
                var userOpt = userRepository.findById(userId);
                if (userOpt.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body("User not found with ID: " + userId);
                }
                
                User user = userOpt.get();
                
                // Check if the user is a doctor
                if (!user.getRole().equals("DOCTOR")) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body("User is not a doctor: " + userId);
                }
                
                // Create a new doctor record
                Doctor newDoctor = new Doctor();
                newDoctor.setUser(user);
                newDoctor.setName(user.getName());
                newDoctor.setEmail(user.getEmail());
                
                // Save the new doctor record
                Doctor savedDoctor = doctorRepository.save(newDoctor);
                
                return ResponseEntity.status(HttpStatus.CREATED).body(savedDoctor);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting or creating doctor: " + e.getMessage());
        }
    }
    
    @GetMapping("/name/{name}")
    public Optional<Doctor> getDoctorByName(@PathVariable String name) {
        return doctorService.getDoctorByName(name);
    }

    @DeleteMapping("/{id}")
    public void deleteDoctor(@PathVariable Long id) {
        doctorService.deleteDoctor(id);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<?> updateDoctor(@PathVariable Long id, @RequestBody Doctor doctorDetails) {
        try {
            Optional<Doctor> existingDoctorOpt = doctorService.getDoctorById(id);
            
            if (!existingDoctorOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Doctor not found");
            }
            
            Doctor existingDoctor = existingDoctorOpt.get();
            
            // Update doctor fields
            if (doctorDetails.getName() != null) existingDoctor.setName(doctorDetails.getName());
            if (doctorDetails.getEmail() != null) existingDoctor.setEmail(doctorDetails.getEmail());
            if (doctorDetails.getPhone() != null) existingDoctor.setPhone(doctorDetails.getPhone());
            if (doctorDetails.getAddress() != null) existingDoctor.setAddress(doctorDetails.getAddress());
            if (doctorDetails.getBio() != null) existingDoctor.setBio(doctorDetails.getBio());
            if (doctorDetails.getLicenseNumber() != null) existingDoctor.setLicenseNumber(doctorDetails.getLicenseNumber());
            if (doctorDetails.getYearsOfExperience() != null) existingDoctor.setYearsOfExperience(doctorDetails.getYearsOfExperience());
            
            // Save updated doctor
            Doctor updatedDoctor = doctorService.updateDoctor(existingDoctor);
            return ResponseEntity.ok(updatedDoctor);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error updating doctor: " + e.getMessage());
        }
    }
    
    @GetMapping("/{id}/availability")
    public ResponseEntity<List<Map<String, Object>>> getDoctorAvailability(
            @PathVariable Long id,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            // Get the doctor's availability schedule for the specific date
            List<DoctorAvailability> availabilities = availabilityService.getDoctorAvailability(id);
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            List<DoctorAvailability> dayAvailabilities = availabilities.stream()
                .filter(avail -> avail.getDayOfWeek() == dayOfWeek)
                .collect(Collectors.toList());

            // Get all appointments for this doctor on this date
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(23, 59, 59);
            List<Appointment> appointments = appointmentService.appointmentRepository.findByDoctorIdAndAppointmentDateBetween(id, startOfDay, endOfDay);

            List<Map<String, Object>> timeSlots = new ArrayList<>();
            for (DoctorAvailability availability : dayAvailabilities) {
                LocalTime startTime = availability.getStartTime();
                LocalTime endTime = availability.getEndTime();
                LocalTime currentTime = startTime;
                while (currentTime.isBefore(endTime)) {
                    LocalTime slotEndTime = currentTime.plusMinutes(30);
                    boolean isBooked = appointments.stream().anyMatch(appt -> {
                        LocalTime apptStart = appt.getAppointmentDate().toLocalTime();
                        LocalTime apptEnd = appt.getEndTime().toLocalTime();
                        return ( !slotEndTime.isAfter(apptStart) && !currentTime.isBefore(apptEnd) ) ? false :
                            ( !currentTime.isAfter(apptStart) && !slotEndTime.isAfter(apptStart) ) ? false :
                            ( !apptEnd.isAfter(currentTime) && !apptStart.isBefore(slotEndTime) ) ? false :
                            ( !(slotEndTime.isBefore(apptStart) || currentTime.isAfter(apptEnd)) );
                    });
                    if (!isBooked) {
                        Map<String, Object> slot = new HashMap<>();
                        String amPm = currentTime.getHour() < 12 ? "AM" : "PM";
                        int displayHour = currentTime.getHour() > 12 ? currentTime.getHour() - 12 : currentTime.getHour();
                        if (displayHour == 0) displayHour = 12;
                        String time = String.format("%d:%02d %s", displayHour, currentTime.getMinute(), amPm);
                        slot.put("time", time);
                        slot.put("startTime", currentTime.toString());
                        slot.put("endTime", slotEndTime.toString());
                        slot.put("isAvailable", true);
                        timeSlots.add(slot);
                    }
                    currentTime = slotEndTime;
                }
            }
            return ResponseEntity.ok(timeSlots);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ArrayList<>());
        }
    }
}
