import React, { useState, useEffect } from "react";
import { Con<PERSON>er, <PERSON>, Col, Card, Button, Form, Alert, Badge, Table, Modal, Spinner } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faCreditCard, 
  faMobile, 
  faCheckCircle, 
  faTimesCircle, 
  faClock,
  faReceipt
} from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

const Payments = () => {
  const { user } = useAuth();
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [paymentForm, setPaymentForm] = useState({
    paymentMethod: "ORANGE_MOBILE_MONEY",
    phoneNumber: "",
    provider: "ORANGE",
    amount: ""
  });
  const [processingPayment, setProcessingPayment] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/payments/patient/${user.id}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      setPayments(response.data);
    } catch (err) {
      console.error("Error fetching payments:", err);
      setError("Failed to load payment history");
      // Mock data for demo
      setPayments([
        {
          id: 1,
          amount: 10000,
          status: "COMPLETED",
          paymentMethod: "ORANGE_MOBILE_MONEY",
          provider: "ORANGE",
          phoneNumber: "678901234",
          transactionId: "TXN-ABC123",
          paymentDate: new Date().toISOString(),
          appointment: { appointmentDate: new Date().toISOString() }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentMethodChange = (method) => {
    setPaymentForm({
      ...paymentForm,
      paymentMethod: method,
      provider: method === "ORANGE_MOBILE_MONEY" ? "ORANGE" : "MTN"
    });
  };

  const handleSubmitPayment = async (e) => {
    e.preventDefault();
    setProcessingPayment(true);
    setError("");

    try {
      // Create payment
      const paymentData = {
        appointmentId: selectedAppointment.id,
        patientId: user.id,
        amount: parseFloat(paymentForm.amount),
        paymentMethod: paymentForm.paymentMethod,
        phoneNumber: paymentForm.phoneNumber,
        provider: paymentForm.provider
      };

      const createResponse = await axios.post(`${API_BASE_URL}/api/payments`, paymentData, {
        headers: { Authorization: `Bearer ${user.token}` }
      });

      const payment = createResponse.data;

      // Process the payment
      const processResponse = await axios.post(
        `${API_BASE_URL}/api/payments/${payment.id}/process`,
        { provider: paymentForm.provider },
        { headers: { Authorization: `Bearer ${user.token}` } }
      );

      const processedPayment = processResponse.data;

      if (processedPayment.status === "COMPLETED") {
        setPaymentSuccess(true);
        setShowPaymentModal(false);
        fetchPayments(); // Refresh payment list
      } else {
        setError("Payment failed. Please check your phone number and try again.");
      }
    } catch (err) {
      console.error("Payment error:", err);
      setError("Payment processing failed. Please try again.");
    } finally {
      setProcessingPayment(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "COMPLETED":
        return <Badge bg="success"><FontAwesomeIcon icon={faCheckCircle} className="me-1" />Completed</Badge>;
      case "PENDING":
        return <Badge bg="warning"><FontAwesomeIcon icon={faClock} className="me-1" />Pending</Badge>;
      case "FAILED":
        return <Badge bg="danger"><FontAwesomeIcon icon={faTimesCircle} className="me-1" />Failed</Badge>;
      case "PROCESSING":
        return <Badge bg="info"><FontAwesomeIcon icon={faClock} className="me-1" />Processing</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case "ORANGE_MOBILE_MONEY":
        return <FontAwesomeIcon icon={faMobile} className="text-warning" />;
      case "MTN_MOBILE_MONEY":
        return <FontAwesomeIcon icon={faMobile} className="text-warning" />;
      default:
        return <FontAwesomeIcon icon={faCreditCard} />;
    }
  };

  const openPaymentModal = (appointment) => {
    setSelectedAppointment(appointment);
    setPaymentForm({
      paymentMethod: "ORANGE_MOBILE_MONEY",
      phoneNumber: "",
      provider: "ORANGE",
      amount: appointment.amount || "10000"
    });
    setShowPaymentModal(true);
    setPaymentSuccess(false);
  };

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2><FontAwesomeIcon icon={faCreditCard} className="me-2" />Payment Management</h2>
          <p className="text-muted">Manage your payments and view payment history</p>
        </Col>
      </Row>

      {paymentSuccess && (
        <Alert variant="success" onClose={() => setPaymentSuccess(false)} dismissible>
          <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
          Payment completed successfully! You will receive a confirmation notification.
        </Alert>
      )}

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">Payment History</h5>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-2">Loading payment history...</p>
                </div>
              ) : payments.length > 0 ? (
                <Table responsive>
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Amount</th>
                      <th>Method</th>
                      <th>Status</th>
                      <th>Transaction ID</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payments.map((payment) => (
                      <tr key={payment.id}>
                        <td>{moment(payment.paymentDate).format('MMM D, YYYY')}</td>
                        <td>{payment.amount?.toLocaleString()} XAF</td>
                        <td>
                          {getPaymentMethodIcon(payment.paymentMethod)}
                          <span className="ms-2">
                            {payment.provider} Mobile Money
                          </span>
                        </td>
                        <td>{getStatusBadge(payment.status)}</td>
                        <td>
                          <small className="text-muted">{payment.transactionId}</small>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <FontAwesomeIcon icon={faReceipt} className="text-muted fa-3x mb-3" />
                  <h5>No payments found</h5>
                  <p className="text-muted">Your payment history will appear here</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">Quick Payment</h5>
            </Card.Header>
            <Card.Body>
              <p className="text-muted mb-3">Make a payment for your upcoming appointment</p>
              
              {/* Mock appointment for demo */}
              <div className="border rounded p-3 mb-3">
                <h6>Appointment with Dr. Smith</h6>
                <p className="mb-1">Date: {moment().add(1, 'day').format('MMM D, YYYY')}</p>
                <p className="mb-2">Time: 10:00 AM</p>
                <p className="mb-0"><strong>Amount: 10,000 XAF</strong></p>
              </div>

              <Button 
                variant="primary" 
                className="w-100"
                onClick={() => openPaymentModal({
                  id: 1,
                  amount: 15000,
                  doctorName: "Dr. Smith",
                  appointmentDate: moment().add(1, 'day').toISOString()
                })}
              >
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Pay Now
              </Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Payment Modal */}
      <Modal show={showPaymentModal} onHide={() => setShowPaymentModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Make Payment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedAppointment && (
            <div className="mb-4">
              <h6>Appointment Details</h6>
              <p><strong>Doctor:</strong> {selectedAppointment.doctorName}</p>
              <p><strong>Date:</strong> {moment(selectedAppointment.appointmentDate).format('MMM D, YYYY')}</p>
              <p><strong>Amount:</strong> {selectedAppointment.amount?.toLocaleString()} XAF</p>
            </div>
          )}

          <Form onSubmit={handleSubmitPayment}>
            <Form.Group className="mb-3">
              <Form.Label>Payment Method</Form.Label>
              <div className="d-flex gap-3">
                <Form.Check
                  type="radio"
                  name="paymentMethod"
                  id="orange"
                  label={
                    <div className="d-flex align-items-center">
                      <FontAwesomeIcon icon={faMobile} className="text-warning me-2" />
                      ORANGE Mobile Money
                    </div>
                  }
                  checked={paymentForm.paymentMethod === "ORANGE_MOBILE_MONEY"}
                  onChange={() => handlePaymentMethodChange("ORANGE_MOBILE_MONEY")}
                />
                <Form.Check
                  type="radio"
                  name="paymentMethod"
                  id="mtn"
                  label={
                    <div className="d-flex align-items-center">
                      <FontAwesomeIcon icon={faMobile} className="text-warning me-2" />
                      MTN Mobile Money
                    </div>
                  }
                  checked={paymentForm.paymentMethod === "MTN_MOBILE_MONEY"}
                  onChange={() => handlePaymentMethodChange("MTN_MOBILE_MONEY")}
                />
              </div>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Phone Number</Form.Label>
              <Form.Control
                type="tel"
                placeholder="Enter your mobile money number"
                value={paymentForm.phoneNumber}
                onChange={(e) => setPaymentForm({...paymentForm, phoneNumber: e.target.value})}
                required
              />
              <Form.Text className="text-muted">
                Enter the phone number registered with your {paymentForm.provider} mobile money account
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Amount (XAF)</Form.Label>
              <Form.Control
                type="number"
                value={paymentForm.amount}
                onChange={(e) => setPaymentForm({...paymentForm, amount: e.target.value})}
                required
                readOnly
              />
            </Form.Group>

            <div className="alert alert-info">
              <strong>Note:</strong> You will receive a prompt on your phone to confirm the payment. 
              Please enter your mobile money PIN when prompted.
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPaymentModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleSubmitPayment}
            disabled={processingPayment}
          >
            {processingPayment ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Processing...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Pay {paymentForm.amount} XAF
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default Payments;
