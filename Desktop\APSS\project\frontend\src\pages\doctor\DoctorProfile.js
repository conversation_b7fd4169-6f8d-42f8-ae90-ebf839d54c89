import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { getDoctorById } from '../../assets/utils/api';

const DoctorProfile = () => {
  const { doctorId } = useParams();
  const [doctor, setDoctor] = useState(null);

  useEffect(() => {
    getDoctorById(doctorId)
      .then(data => {
        setDoctor(data);
      })
      .catch(error => {
        console.error('Error fetching doctor data:', error);
      });
  }, [doctorId]);

  if (!doctor) {
    return <p>Loading...</p>;
  }

  return (
    <div className="container mt-5">
      <h1>{doctor.name}</h1>
      <p>Specialty: {doctor.specialty?.name}</p>
      <p>{doctor.bio}</p>
      <p><strong>Email:</strong> {doctor.email}</p>
      <p><strong>Phone:</strong> {doctor.phone}</p>
      <button className="btn btn-primary">Book Appointment</button>
    </div>
  );
};

export default DoctorProfile;
