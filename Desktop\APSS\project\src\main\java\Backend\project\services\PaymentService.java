package Backend.project.services;

import Backend.project.model.*;
import Backend.project.repositories.PaymentRepository;
import Backend.project.repositories.AppointmentRepository;
import Backend.project.repositories.PatientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class PaymentService {

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * Create a new payment for an appointment
     */
    public Payment createPayment(Long appointmentId, Long patientId, BigDecimal amount, 
                                PaymentMethod paymentMethod, String phoneNumber, String provider) {
        
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        Optional<Patient> patientOpt = patientRepository.findById(patientId);
        
        if (appointmentOpt.isEmpty()) {
            throw new IllegalArgumentException("Appointment not found");
        }
        
        if (patientOpt.isEmpty()) {
            throw new IllegalArgumentException("Patient not found");
        }
        
        Appointment appointment = appointmentOpt.get();
        Patient patient = patientOpt.get();
        
        // Generate transaction ID
        String transactionId = generateTransactionId();
        
        Payment payment = new Payment(appointment, patient, amount, paymentMethod, phoneNumber, provider);
        payment.setTransactionId(transactionId);
        payment.setPaymentDate(LocalDateTime.now());
        
        return paymentRepository.save(payment);
    }

    /**
     * Process mobile money payment (ORANGE or MTN)
     */
    public Payment processMobileMoneyPayment(Long paymentId, String provider) {
        Optional<Payment> paymentOpt = paymentRepository.findById(paymentId);
        
        if (paymentOpt.isEmpty()) {
            throw new IllegalArgumentException("Payment not found");
        }
        
        Payment payment = paymentOpt.get();
        
        // Simulate mobile money payment processing
        // In a real implementation, this would integrate with ORANGE/MTN APIs
        try {
            // Simulate API call to mobile money provider
            boolean paymentSuccess = simulateMobileMoneyPayment(payment, provider);
            
            if (paymentSuccess) {
                payment.setStatus(PaymentStatus.COMPLETED);
                payment.setPaymentDate(LocalDateTime.now());
                
                // Send notification to patient
                notificationService.sendPaymentConfirmationNotification(payment);
                
                return paymentRepository.save(payment);
            } else {
                payment.setStatus(PaymentStatus.FAILED);
                payment.setNotes("Payment failed - insufficient funds or invalid phone number");
                return paymentRepository.save(payment);
            }
        } catch (Exception e) {
            payment.setStatus(PaymentStatus.FAILED);
            payment.setNotes("Payment processing error: " + e.getMessage());
            return paymentRepository.save(payment);
        }
    }

    /**
     * Get payments by patient
     */
    public List<Payment> getPaymentsByPatient(Long patientId) {
        return paymentRepository.findByPatientId(patientId);
    }

    /**
     * Get payment by ID
     */
    public Optional<Payment> getPaymentById(Long paymentId) {
        return paymentRepository.findById(paymentId);
    }

    /**
     * Update payment status
     */
    public Payment updatePaymentStatus(Long paymentId, PaymentStatus status) {
        Optional<Payment> paymentOpt = paymentRepository.findById(paymentId);
        
        if (paymentOpt.isEmpty()) {
            throw new IllegalArgumentException("Payment not found");
        }
        
        Payment payment = paymentOpt.get();
        payment.setStatus(status);
        
        if (status == PaymentStatus.COMPLETED) {
            payment.setPaymentDate(LocalDateTime.now());
        }
        
        return paymentRepository.save(payment);
    }

    /**
     * Generate unique transaction ID
     */
    private String generateTransactionId() {
        return "TXN-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * Simulate mobile money payment processing
     * In production, this would integrate with actual ORANGE/MTN APIs
     */
    private boolean simulateMobileMoneyPayment(Payment payment, String provider) {
        // Simulate payment processing delay
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Simulate success/failure based on phone number format
        String phoneNumber = payment.getPhoneNumber();
        
        // Basic validation for Cameroon phone numbers
        if (provider.equals("ORANGE")) {
            // ORANGE numbers start with 6, 7, 8, 9
            return phoneNumber.matches("^6[0-9]{8}$|^7[0-9]{8}$|^8[0-9]{8}$|^9[0-9]{8}$");
        } else if (provider.equals("MTN")) {
            // MTN numbers start with 6, 7, 8, 9
            return phoneNumber.matches("^6[0-9]{8}$|^7[0-9]{8}$|^8[0-9]{8}$|^9[0-9]{8}$");
        }
        
        return false;
    }
} 