package Backend.project.controller;

import Backend.project.model.Specialty;
import Backend.project.repositories.SpecialtyRepository;
import Backend.project.services.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controller for public endpoints that can be accessed by all users without authentication
 */
@RestController
@RequestMapping("/api/public")
@CrossOrigin(origins = "http://localhost:3000")
public class PublicController {

    @Autowired
    private SpecialtyRepository specialtyRepository;
    
    @Autowired
    private EmailService emailService;
    
    @Value("${app.admin.email:<EMAIL>}")
    private String adminEmail;

    /**
     * Get all specialties
     * @return List of all specialties
     */
    @GetMapping("/specialties")
    public ResponseEntity<List<Specialty>> getAllSpecialties() {
        List<Specialty> specialties = specialtyRepository.findAll();
        return ResponseEntity.ok(specialties);
    }

    /**
     * Get specialty by ID
     * @param id Specialty ID
     * @return Specialty if found, 404 otherwise
     */
    @GetMapping("/specialties/{id}")
    public ResponseEntity<?> getSpecialtyById(@PathVariable Long id) {
        Optional<Specialty> specialtyOpt = specialtyRepository.findById(id);
        if (specialtyOpt.isPresent()) {
            return ResponseEntity.ok(specialtyOpt.get());
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Specialty not found");
    }
    
    /**
     * Handle contact form submissions via the public API
     * @param contactRequest Map containing contact form data (name, email, subject, message)
     * @return Success message if email is sent
     */
    @PostMapping("/contact-us")
    public ResponseEntity<?> submitContactForm(@RequestBody Map<String, String> contactRequest) {
        try {
            // Extract form data
            String name = contactRequest.get("name");
            String email = contactRequest.get("email");
            String subject = contactRequest.get("subject");
            String message = contactRequest.get("message");
            
            if (name == null || email == null || subject == null || message == null) {
                return ResponseEntity.badRequest().body("Missing required fields");
            }
            
            // Use the enhanced contact form submission handler
            emailService.handleContactFormSubmission(name, email, subject, message, adminEmail);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Contact form submitted successfully. We'll get back to you soon!"
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "Failed to process contact form: " + e.getMessage()
                ));
        }
    }
}
