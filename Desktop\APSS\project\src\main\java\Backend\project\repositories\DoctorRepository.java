package Backend.project.repositories;

// import Backend.project.model.Appointment;
import Backend.project.model.Doctor;
// import Backend.project.model.Patient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorRepository extends JpaRepository<Doctor, Long> {
    List<Doctor> findBySpecialtyId(Long specialtyId);

    Optional<Doctor> findByName(String name);
    
    Optional<Doctor> findByUserId(Long userId);
    boolean existsBySpecialtyId(Long specialtyId);
}
