/* Home Page */
.home {
  text-align: center;
  padding: 10px;
}
.home-page {
  background: #f9f9f9;
}

.home-page .btn {
  display: inline-block;
  padding: 10px 20px;
  margin-top: 0px;
  font-size: 1rem;
  background: #007bff;
  color: blue;
  text-decoration: none;
  border-radius: 5px;
}

.home-page .btn:hover {
  background: #0056b3;
}

/* Login Page */
.login-container {
  width: 100%;
  max-width: 400px;
  margin: auto;
  background: white;
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.login-container h2 {
  margin-bottom: 20px;
}

/* Dashboard */
.dashboard {
  display: flex;
  gap: 20px;
}

.dashboard-sidebar {
  width: 250px;
  background: var(--primary-color);
  color: white;
  padding: 20px;
}

.dashboard-content {
  flex-grow: 1;
  background: white;
  padding: 20px;
  border-radius: var(--border-radius);
}
.page-container {
  padding: 20px;
}

.page-container h1 {
  text-align: center;
  margin-bottom: 20px;
}

.page-container p {
  text-align: center;
  margin-bottom: 20px;
}

.home-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.home-actions .btn {
  width: 150px;
}

.dashboard-container {
  display: flex;
}

.dashboard-content {
  margin-left: 250px;
  padding: 20px;
}

/* General Page Styling */
.home-page,
.about-page,
.contact-page,
.faq-page,
.terms-page,
.privacy-policy-page,
.not-found-page {
  padding: 40px;
  text-align: center;
}

/* Typography */
h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #333;
}

p {
  font-size: 1.2rem;
  color: #555;
}

/* Not Found Page */
.not-found-page a {
  display: inline-block;
  margin-top: 20px;
  color: #007bff;
  font-weight: bold;
}

.not-found-page a:hover {
  text-decoration: underline;
}




