<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Specialties API</title>
    <script>
        async function testSpecialtiesAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Fetching specialties...';
            
            try {
                const response = await fetch('http://localhost:8080/api/public/specialties', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `<h3>Success! Found ${data.length} specialties:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error fetching specialties:</h3>
                    <pre style="color: red">${error.message}</pre>`;
                console.error('Error:', error);
            }
        }
    </script>
</head>
<body>
    <h1>Debug Specialties API</h1>
    <button onclick="testSpecialtiesAPI()">Test Specialties API</button>
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
</body>
</html>
