package Backend.project.controller;

import Backend.project.model.Patient;
import Backend.project.services.PatientService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/patients")
@CrossOrigin(origins = "http://localhost:3000")
public class PatientController {

    private final PatientService userService;

    public PatientController(PatientService userService) {
        this.userService = userService;
    }

    @PostMapping
    public Patient createUser(@RequestBody Patient user) {
        return userService.createUser(user);
    }

    @GetMapping("/{id}")
    public Optional<Patient> getUserById(@PathVariable Long id) {
        return userService.getUserById(id);
    }
    @GetMapping("/email/{email}")
    public Optional<Patient> getUserByEmail(@PathVariable String email) {
        return userService.getUserByEmail(email);
    }

    @GetMapping
    public List<Patient> getAllUsers() {
        return userService.getAllUsers();
    }

    @DeleteMapping("/{id}")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<?> updatePatient(@PathVariable Long id, @RequestBody Patient patientDetails) {
        try {
            Optional<Patient> existingPatientOpt = userService.getUserById(id);
            
            if (!existingPatientOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Patient not found");
            }
            
            Patient existingPatient = existingPatientOpt.get();
            
            // Update patient fields
            if (patientDetails.getName() != null) existingPatient.setName(patientDetails.getName());
            if (patientDetails.getEmail() != null) existingPatient.setEmail(patientDetails.getEmail());
            if (patientDetails.getPhone() != null) existingPatient.setPhone(patientDetails.getPhone());
            if (patientDetails.getAddress() != null) existingPatient.setAddress(patientDetails.getAddress());
            if (patientDetails.getDateOfBirth() != null) existingPatient.setDateOfBirth(patientDetails.getDateOfBirth());
            if (patientDetails.getGender() != null) existingPatient.setGender(patientDetails.getGender());
            if (patientDetails.getBloodGroup() != null) existingPatient.setBloodGroup(patientDetails.getBloodGroup());
            if (patientDetails.getAllergies() != null) existingPatient.setAllergies(patientDetails.getAllergies());
            if (patientDetails.getMedicalHistory() != null) existingPatient.setMedicalHistory(patientDetails.getMedicalHistory());
            
            // Save updated patient
            Patient updatedPatient = userService.updatePatient(existingPatient);
            return ResponseEntity.ok(updatedPatient);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error updating patient: " + e.getMessage());
        }
    }
}
