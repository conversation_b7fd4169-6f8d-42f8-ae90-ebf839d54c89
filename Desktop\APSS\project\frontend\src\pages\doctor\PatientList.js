import React from "react";
import "../styles/pages.css";

const PatientList = () => {
  const patients = [
    { id: 1, name: "<PERSON>", condition: "Diabetes" },
    { id: 2, name: "<PERSON>", condition: "Hypertension" },
  ];

  return (
    <div className="patient-list">
      <h1>Patient List</h1>
      <ul>
        {patients.map((patient) => (
          <li key={patient.id}>
            {patient.name} - {patient.condition}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PatientList;
