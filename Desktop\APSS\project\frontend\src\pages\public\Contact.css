.contact-container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-container .card {
  border-radius: 10px;
  transition: all 0.3s ease;
}

.contact-container .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.contact-info h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.contact-info p {
  font-size: 1rem;
  line-height: 1.5;
}

.map-container {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Form styling */
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control {
  padding: 0.75rem;
}

textarea.form-control {
  min-height: 120px;
}

/* Button styling */
.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .contact-container h1 {
    font-size: 2rem;
  }
  
  .contact-container .lead {
    font-size: 1rem;
  }
}
