package Backend.project.services;

import Backend.project.model.Notification;
import Backend.project.model.Appointment;
import Backend.project.model.Payment;
import Backend.project.repositories.NotificationRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.time.LocalDateTime;

@Service
public class NotificationService {

    private final NotificationRepository notificationRepository;

    public NotificationService(NotificationRepository notificationRepository) {
        this.notificationRepository = notificationRepository;
    }

    public List<Notification> getAllNotifications() {
        return notificationRepository.findAll();
    }

    public Notification getNotificationById(Long id) {
        return notificationRepository.findById(id).orElse(null);
    }

    public Notification createNotification(Notification notification) {
        return notificationRepository.save(notification);
    }

    public Notification updateNotification(Long id, Notification notification) {
        notification.setId(id);
        return notificationRepository.save(notification);
    }

    public void deleteNotification(Long id) {
        notificationRepository.deleteById(id);
    }
    
    /**
     * Sends a confirmation notification for an appointment
     * @param appointment The appointment that was confirmed
     */
    public void sendAppointmentConfirmationNotification(Appointment appointment) {
        Notification notification = new Notification();
        notification.setPatient(appointment.getPatient());
        notification.setMessage("Your appointment on " + appointment.getAppointmentDate() + 
                              " has been confirmed.");
        notification.setDateTimeSent(LocalDateTime.now());
        
        createNotification(notification);
    }
    
    /**
     * Sends a cancellation notification for an appointment
     * @param appointment The appointment that was canceled
     */
    public void sendAppointmentCancellationNotification(Appointment appointment) {
        Notification notification = new Notification();
        notification.setPatient(appointment.getPatient());
        notification.setMessage("Your appointment on " + appointment.getAppointmentDate() + 
                              " has been canceled.");
        notification.setDateTimeSent(LocalDateTime.now());
        
        createNotification(notification);
    }
    
    /**
     * Sends a notification to the doctor about a new appointment request
     * @param appointment The appointment that was requested
     */
    public void sendAppointmentRequestNotification(Appointment appointment) {
        Notification notification = new Notification();
        notification.setPatient(appointment.getPatient());
        notification.setMessage("New appointment request from " + appointment.getPatient().getName() + 
                              " on " + appointment.getAppointmentDate() + 
                              " for reason: " + appointment.getReason());
        notification.setDateTimeSent(LocalDateTime.now());
        
        createNotification(notification);
    }
    
    /**
     * Sends a payment confirmation notification to the patient
     * @param payment The payment that was completed
     */
    public void sendPaymentConfirmationNotification(Payment payment) {
        Notification notification = new Notification();
        notification.setPatient(payment.getPatient());
        notification.setMessage("Payment of " + payment.getAmount() + " XAF for appointment on " + 
                              payment.getAppointment().getAppointmentDate() + " has been confirmed. " +
                              "Transaction ID: " + payment.getTransactionId());
        notification.setDateTimeSent(LocalDateTime.now());
        
        createNotification(notification);
    }
    
    /**
     * Sends a payment failure notification to the patient
     * @param payment The payment that failed
     */
    public void sendPaymentFailureNotification(Payment payment) {
        Notification notification = new Notification();
        notification.setPatient(payment.getPatient());
        notification.setMessage("Payment of " + payment.getAmount() + " XAF for appointment on " + 
                              payment.getAppointment().getAppointmentDate() + " has failed. " +
                              "Please try again or contact support.");
        notification.setDateTimeSent(LocalDateTime.now());
        
        createNotification(notification);
    }
}
