import React from "react";
import { Routes, Route } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";
import Dashboard from "../pages/patient/Dashboard";
import Profile from "../pages/patient/Profile";
import BookAppointment from "../pages/patient/BookAppointment";
import AppointmentHistory from "../pages/patient/AppointmentHistory";
import AppointmentDetails from "../pages/patient/AppointmentsDetails";
import MedicalRecords from "../pages/patient/MedicalRecord";
import Payments from "../pages/patient/Payments";
import Messages from "../pages/patient/Message";
import Feedback from "../pages/patient/Feedback";
import Notifications from "../pages/patient/Notifications";

const PatientRoutes = ({ isAuthenticated }) => {
  return (
    <Routes>
      <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} allowedRoles={["patient"]} />}>
        <Route path="/patient/dashboard" element={<Dashboard />} />
        <Route path="/patient/profile" element={<Profile />} />
        <Route path="/patient/book" element={<BookAppointment />} />
        <Route path="/patient/appointments" element={<AppointmentHistory />} />
        <Route path="/patient/appointment/:id" element={<AppointmentDetails />} />
        <Route path="/patient/records" element={<MedicalRecords />} />
        <Route path="/patient/payments" element={<Payments />} />
        <Route path="/patient/messages" element={<Messages />} />
        <Route path="/patient/feedback" element={<Feedback />} />
        <Route path="/patient/notifications" element={<Notifications />} />
      </Route>
    </Routes>
  );
};

export default PatientRoutes;
