package Backend.project.services;

import Backend.project.model.Doctor;
import Backend.project.model.DoctorAvailability;
import Backend.project.repositories.DoctorAvailabilityRepository;
import Backend.project.repositories.DoctorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Service
public class DoctorAvailabilityService {

    @Autowired
    private DoctorAvailabilityRepository doctorAvailabilityRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    /**
     * Get all availability slots for a doctor
     */
    public List<DoctorAvailability> getDoctorAvailability(Long doctorId) {
        return doctorAvailabilityRepository.findByDoctorId(doctorId);
    }

    /**
     * Get availability for a specific day of the week
     */
    public List<DoctorAvailability> getDoctorAvailabilityByDay(Long doctorId, DayOfWeek dayOfWeek) {
        return doctorAvailabilityRepository.findByDoctorIdAndDayOfWeek(doctorId, dayOfWeek);
    }

    /**
     * Add a new availability slot for a doctor
     */
    public DoctorAvailability addAvailabilitySlot(Long doctorId, DayOfWeek dayOfWeek, 
                                                 LocalTime startTime, LocalTime endTime) {
        Optional<Doctor> doctorOpt = doctorRepository.findById(doctorId);
        if (doctorOpt.isEmpty()) {
            throw new IllegalArgumentException("Doctor not found with ID: " + doctorId);
        }

        Doctor doctor = doctorOpt.get();
        DoctorAvailability availability = new DoctorAvailability(
                doctor, dayOfWeek, startTime, endTime, true);
        
        return doctorAvailabilityRepository.save(availability);
    }

    /**
     * Update an existing availability slot
     */
    public DoctorAvailability updateAvailabilitySlot(Long availabilityId, DayOfWeek dayOfWeek,
                                                    LocalTime startTime, LocalTime endTime, boolean isAvailable) {
        Optional<DoctorAvailability> availabilityOpt = doctorAvailabilityRepository.findById(availabilityId);
        if (availabilityOpt.isEmpty()) {
            throw new IllegalArgumentException("Availability slot not found with ID: " + availabilityId);
        }

        DoctorAvailability availability = availabilityOpt.get();
        availability.setDayOfWeek(dayOfWeek);
        availability.setStartTime(startTime);
        availability.setEndTime(endTime);
        availability.setAvailable(isAvailable);
        
        return doctorAvailabilityRepository.save(availability);
    }

    /**
     * Delete an availability slot
     */
    public void deleteAvailabilitySlot(Long availabilityId) {
        doctorAvailabilityRepository.deleteById(availabilityId);
    }

    /**
     * Check if a doctor is available at a specific date and time
     */
    public boolean isDoctorAvailable(Long doctorId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        // Check if the doctor exists
        if (!doctorRepository.existsById(doctorId)) {
            throw new IllegalArgumentException("Doctor not found with ID: " + doctorId);
        }
        
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        List<DoctorAvailability> availabilitySlots = doctorAvailabilityRepository.findByDoctorIdAndDayOfWeek(doctorId, dayOfWeek);
        
        // If the doctor has no availability slots configured, consider them available
        // This allows new doctors to accept appointments before setting up their schedule
        if (availabilitySlots.isEmpty()) {
            return true;
        }
        
        // Check if the requested time falls within any of the doctor's available slots
        for (DoctorAvailability slot : availabilitySlots) {
            if (slot.isAvailable() && 
                !startTime.isBefore(slot.getStartTime()) && 
                !endTime.isAfter(slot.getEndTime())) {
                return true;
            }
        }
        
        return false;
    }
}
