import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Accordion, <PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faQuestionCircle, faCalendarCheck, faClock, faUserMd, faCreditCard, faHospital, faIdCard } from "@fortawesome/free-solid-svg-icons";
import "./FAQ.css";

const FAQ = () => {
  const [activeKey, setActiveKey] = useState(null);
  
  // Toggle function to handle accordion state
  const handleToggle = (eventKey) => {
    setActiveKey(activeKey === eventKey ? null : eventKey);
  };
  
  const faqData = [
    {
      id: "1",
      question: "How do I book an appointment?",
      answer: "Booking an appointment is easy! Simply register for an account, log in, and navigate to the 'Book Appointment' section. You can search for doctors by specialty, view their available time slots, and choose the one that works best for you. Once confirmed, you'll receive an email with your appointment details.",
      icon: faC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    },
    {
      id: "2",
      question: "What services do you offer?",
      answer: "Our clinic provides a wide range of medical services including general check-ups, specialist consultations, preventive care, diagnostic services, and emergency care. Our team of qualified doctors covers various specialties including cardiology, pediatrics, dermatology, orthopedics, and more.",
      icon: faHospital
    },
    {
      id: "3",
      question: "How can I cancel or reschedule my appointment?",
      answer: "You can cancel or reschedule your appointment by logging into your account and going to 'My Appointments' section. Find the appointment you wish to modify, and click on the 'Cancel' or 'Reschedule' button. Please note that cancellations made less than 24 hours before the appointment may incur a fee.",
      icon: faClock
    },
    {
      id: "4",
      question: "How do I find the right doctor for my condition?",
      answer: "You can find the right doctor by using our search filters to narrow down specialists based on your medical condition. Each doctor profile includes their qualifications, experience, specialties, and patient reviews to help you make an informed decision. You can also contact our helpdesk for personalized recommendations.",
      icon: faUserMd
    },
    {
      id: "5",
      question: "What payment methods do you accept?",
      answer: "We accept various payment methods including credit/debit cards, health insurance, and online payment services. You can pay for your appointment at the time of booking or at the clinic after your consultation. We also provide itemized receipts for insurance reimbursement purposes.",
      icon: faCreditCard
    },
    {
      id: "6",
      question: "Do I need to bring anything to my appointment?",
      answer: "Yes, please bring your ID, insurance card (if applicable), a list of current medications, and any relevant medical records or test results. If you're a new patient, arriving 15 minutes early to complete registration forms is recommended. You may also want to prepare a list of questions or symptoms to discuss with your doctor.",
      icon: faIdCard
    }
  ];

  return (
    <Container className="py-5 faq-container">
      <Row className="justify-content-center mb-5">
        <Col md={8} className="text-center">
          <h1 className="display-4 mb-4">
            <FontAwesomeIcon icon={faQuestionCircle} className="me-3 text-primary" />
            Frequently Asked Questions
          </h1>
          <p className="lead text-muted">
            Find answers to common questions about our services, appointment booking, and more.
          </p>
        </Col>
      </Row>
      
      <Row className="justify-content-center">
        <Col md={8}>
          <Accordion activeKey={activeKey} onSelect={handleToggle} className="faq-accordion">
            {faqData.map((faq, index) => (
              <Accordion.Item eventKey={faq.id} key={faq.id} className="mb-3 shadow-sm">
                <Accordion.Header>
                  <FontAwesomeIcon icon={faq.icon} className="me-3 text-primary" />
                  {faq.question}
                </Accordion.Header>
                <Accordion.Body>
                  <div className="answer-content">
                    <p className="mb-0">{faq.answer}</p>
                  </div>
                </Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>
        </Col>
      </Row>
      
      <Row className="justify-content-center mt-5">
        <Col md={8} className="text-center">
          <Card className="bg-light p-4">
            <Card.Body>
              <h4>Still have questions?</h4>
              <p className="mb-4">Our support team is here to help you with any other questions you might have.</p>
              <Button variant="primary" href="/contact">
                Contact Us
              </Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default FAQ;
