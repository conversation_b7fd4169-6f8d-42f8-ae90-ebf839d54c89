import React from "react";
import { Routes, Route } from "react-router-dom";
import Home from "../pages/public/HomePage";
import About from "../pages/public/About";
import Contact from "../pages/public/Contact";
import FAQ from "../pages/public/FAQ";
import Terms from "../pages/public/Terms";
import PrivacyPolicy from "../pages/public/PrivacyPolicy";
import NotFound from "../pages/public/ErrorPage";
import Login from "../pages/auth/Login";
import Register from "../pages/auth/Register";

const PublicRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/faq" element={<FAQ />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<PrivacyPolicy />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="*" element={<ErrorPage />} />
    </Routes>
  );
};

export default PublicRoute;
