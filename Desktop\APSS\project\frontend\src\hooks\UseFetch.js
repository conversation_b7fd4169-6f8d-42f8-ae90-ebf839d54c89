import { useState, useEffect, useCallback } from "react";
import api from "../assets/utils/api";

const useFetch = (endpoint, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refetchIndex, setRefetchIndex] = useState(0);

  // Use a stable reference for options to prevent unnecessary re-renders
  const optionsString = JSON.stringify(options);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const method = options.method?.toLowerCase() || 'get';
      const config = {
        headers: options.headers || {},
        params: options.params || {}
      };
      
      let response;
      if (method === 'post' || method === 'put' || method === 'patch') {
        response = await api[method](endpoint, options.body, config);
      } else {
        response = await api[method](endpoint, config);
      }
      
      setData(response.data);
    } catch (err) {
      console.error("API request failed:", err);
      setError(err.message || "An error occurred while fetching data");
      
      // If mockData is provided in options, use it as fallback
      if (options.mockData) {
        console.log("Using mock data fallback");
        setData(options.mockData);
        setError(null); // Clear error when using mock data
      }
    } finally {
      setLoading(false);
    }
  }, [endpoint, optionsString]);

  // Trigger the fetch when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData, refetchIndex]);

  // Function to manually refetch data
  const refetch = () => {
    setRefetchIndex(prev => prev + 1);
  };

  return { data, loading, error, refetch };
};

export default useFetch;
