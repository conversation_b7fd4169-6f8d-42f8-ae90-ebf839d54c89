import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Al<PERSON>, Spin<PERSON>, ListGroup, Badge, Rating } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faStar, faComment, faUserMd, faCalendarAlt, faThumbsUp, faThumbsDown } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

const Feedback = () => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [feedbacks, setFeedbacks] = useState([]);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [feedbackForm, setFeedbackForm] = useState({
    rating: 5,
    comment: "",
    category: "GENERAL"
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  useEffect(() => {
    fetchAppointments();
    fetchFeedbacks();
  }, []);

  const fetchAppointments = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/appointments/patient/${user.id}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      // Filter for completed appointments that haven't been reviewed
      const completedAppointments = (response.data || []).filter(appt => 
        appt.status === "COMPLETED" && !appt.hasFeedback
      );
      setAppointments(completedAppointments);
    } catch (err) {
      console.error("Error fetching appointments:", err);
      // Mock data for demo
      setAppointments([
        {
          id: 1,
          appointmentDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Smith",
          doctorSpecialty: "Cardiology",
          reason: "Heart checkup",
          status: "COMPLETED",
          hasFeedback: false
        },
        {
          id: 2,
          appointmentDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Johnson",
          doctorSpecialty: "Neurology",
          reason: "Headache consultation",
          status: "COMPLETED",
          hasFeedback: false
        }
      ]);
    }
  };

  const fetchFeedbacks = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/feedback/patient/${user.id}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      setFeedbacks(response.data || []);
    } catch (err) {
      console.error("Error fetching feedbacks:", err);
      setError("Failed to load feedback history");
      // Mock data for demo
      setFeedbacks([
        {
          id: 1,
          appointmentId: 1,
          doctorName: "Dr. Smith",
          rating: 5,
          comment: "Excellent consultation. Doctor was very professional and helpful.",
          category: "GENERAL",
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitFeedback = async (e) => {
    e.preventDefault();
    if (!selectedAppointment) return;

    setSubmitting(true);
    try {
      const feedbackData = {
        appointmentId: selectedAppointment.id,
        patientId: user.id,
        doctorId: selectedAppointment.doctorId,
        rating: feedbackForm.rating,
        comment: feedbackForm.comment,
        category: feedbackForm.category
      };

      const response = await axios.post(`${API_BASE_URL}/api/feedback`, feedbackData, {
        headers: { Authorization: `Bearer ${user.token}` }
      });

      setSuccess("Feedback submitted successfully!");
      setFeedbacks([...feedbacks, response.data]);
      setSelectedAppointment(null);
      setFeedbackForm({ rating: 5, comment: "", category: "GENERAL" });
      
      // Refresh appointments to mark as reviewed
      fetchAppointments();
    } catch (err) {
      console.error("Error submitting feedback:", err);
      setError("Failed to submit feedback. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleAppointmentSelect = (appointment) => {
    setSelectedAppointment(appointment);
    setError("");
    setSuccess("");
  };

  const getRatingStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <FontAwesomeIcon
        key={index}
        icon={faStar}
        className={index < rating ? "text-warning" : "text-muted"}
      />
    ));
  };

  const getCategoryBadge = (category) => {
    switch (category) {
      case "GENERAL":
        return <Badge bg="primary">General</Badge>;
      case "TREATMENT":
        return <Badge bg="success">Treatment</Badge>;
      case "COMMUNICATION":
        return <Badge bg="info">Communication</Badge>;
      case "FACILITY":
        return <Badge bg="secondary">Facility</Badge>;
      default:
        return <Badge bg="secondary">{category}</Badge>;
    }
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading feedback data...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FontAwesomeIcon icon={faComment} className="me-2" />
            Feedback & Reviews
          </h2>
          <p className="text-muted">Share your experience and help improve our services</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" onClose={() => setSuccess("")} dismissible>
          {success}
        </Alert>
      )}

      <Row>
        <Col lg={6}>
          <Card className="shadow-sm mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                Provide Feedback
              </h5>
            </Card.Header>
            <Card.Body>
              {appointments.length > 0 ? (
                <>
                  <p className="text-muted mb-3">Select a completed appointment to provide feedback:</p>
                  <ListGroup>
                    {appointments.map((appointment) => (
                      <ListGroup.Item
                        key={appointment.id}
                        action
                        active={selectedAppointment?.id === appointment.id}
                        onClick={() => handleAppointmentSelect(appointment)}
                        className="d-flex justify-content-between align-items-center"
                      >
                        <div>
                          <strong>{appointment.doctorName}</strong>
                          <div className="text-muted small">
                            {moment(appointment.appointmentDate).format("MMM D, YYYY")} - {appointment.reason}
                          </div>
                        </div>
                        {selectedAppointment?.id === appointment.id && (
                          <Badge bg="primary">Selected</Badge>
                        )}
                      </ListGroup.Item>
                    ))}
                  </ListGroup>

                  {selectedAppointment && (
                    <Form onSubmit={handleSubmitFeedback} className="mt-4">
                      <Form.Group className="mb-3">
                        <Form.Label>Rating</Form.Label>
                        <div className="d-flex align-items-center">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <FontAwesomeIcon
                              key={star}
                              icon={faStar}
                              className={`me-1 ${star <= feedbackForm.rating ? "text-warning" : "text-muted"}`}
                              style={{ cursor: "pointer", fontSize: "1.5rem" }}
                              onClick={() => setFeedbackForm({ ...feedbackForm, rating: star })}
                            />
                          ))}
                          <span className="ms-2 text-muted">({feedbackForm.rating}/5)</span>
                        </div>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label>Category</Form.Label>
                        <Form.Select
                          value={feedbackForm.category}
                          onChange={(e) => setFeedbackForm({ ...feedbackForm, category: e.target.value })}
                        >
                          <option value="GENERAL">General Experience</option>
                          <option value="TREATMENT">Treatment Quality</option>
                          <option value="COMMUNICATION">Communication</option>
                          <option value="FACILITY">Facility & Environment</option>
                        </Form.Select>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label>Comment</Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={4}
                          value={feedbackForm.comment}
                          onChange={(e) => setFeedbackForm({ ...feedbackForm, comment: e.target.value })}
                          placeholder="Share your experience and suggestions..."
                          required
                        />
                      </Form.Group>

                      <Button
                        type="submit"
                        variant="primary"
                        disabled={submitting}
                        className="w-100"
                      >
                        {submitting ? (
                          <>
                            <Spinner
                              as="span"
                              animation="border"
                              size="sm"
                              role="status"
                              aria-hidden="true"
                              className="me-2"
                            />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <FontAwesomeIcon icon={faComment} className="me-2" />
                            Submit Feedback
                          </>
                        )}
                      </Button>
                    </Form>
                  )}
                </>
              ) : (
                <div className="text-center py-4">
                  <FontAwesomeIcon icon={faCalendarAlt} className="text-muted fa-3x mb-3" />
                  <h5>No completed appointments</h5>
                  <p className="text-muted">You can provide feedback for completed appointments</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={6}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faStar} className="me-2" />
                Your Feedback History
              </h5>
            </Card.Header>
            <Card.Body>
              {feedbacks.length > 0 ? (
                <div>
                  {feedbacks.map((feedback) => (
                    <Card key={feedback.id} className="mb-3 border-0 bg-light">
                      <Card.Body>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div>
                            <h6 className="mb-1">{feedback.doctorName}</h6>
                            <small className="text-muted">
                              {moment(feedback.createdAt).format("MMM D, YYYY")}
                            </small>
                          </div>
                          <div>
                            {getCategoryBadge(feedback.category)}
                          </div>
                        </div>
                        <div className="mb-2">
                          {getRatingStars(feedback.rating)}
                        </div>
                        <p className="mb-0">{feedback.comment}</p>
                      </Card.Body>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <FontAwesomeIcon icon={faStar} className="text-muted fa-3x mb-3" />
                  <h5>No feedback yet</h5>
                  <p className="text-muted">Your feedback history will appear here</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Feedback;
