import React from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import PatientSidebar from "./PatientSidebar";
import DoctorS<PERSON>bar from "./DoctorSidebar";
import AdminSidebar from "./AdminSidebar";
import Footer from "./Footer";
import "./Layout.css";

const Layout = ({ children }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  
  // Check if we're on the home page (footer should only show on home page)
  const isHomePage = location.pathname === "/";
  
  // Determine which sidebar to show based on user role
  const renderSidebar = () => {
    if (!user) return null;
    
    const role = user.role?.toLowerCase();
    
    switch (role) {
      case "patient":
        return <PatientSidebar onLogout={logout} />;
      case "doctor":
        return <DoctorSidebar onLogout={logout} />;
      case "admin":
        return <AdminSidebar onLogout={logout} />;
      default:
        return null;
    }
  };
  
  const sidebar = renderSidebar();
  const hasSidebar = !!sidebar;
  
  return (
    <div className="layout">
      {sidebar}
      <div className={`main-content ${hasSidebar ? 'with-sidebar' : ''}`}>
        {children}
        {isHomePage && <Footer />}
      </div>
    </div>
  );
};

export default Layout; 