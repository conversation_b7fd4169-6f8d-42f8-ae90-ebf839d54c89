package Backend.project.controller;

import Backend.project.model.*;
import Backend.project.services.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/payments")
@CrossOrigin(origins = "http://localhost:3000")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    /**
     * Create a new payment
     */
    @PostMapping
    @PreAuthorize("hasAuthority('PATIENT')")
    public ResponseEntity<?> createPayment(@RequestBody Map<String, Object> requestData) {
        try {
            Long appointmentId = Long.valueOf(requestData.get("appointmentId").toString());
            Long patientId = Long.valueOf(requestData.get("patientId").toString());
            BigDecimal amount = new BigDecimal(requestData.get("amount").toString());
            String paymentMethodStr = (String) requestData.get("paymentMethod");
            String phoneNumber = (String) requestData.get("phoneNumber");
            String provider = (String) requestData.get("provider");

            PaymentMethod paymentMethod = PaymentMethod.valueOf(paymentMethodStr);

            Payment payment = paymentService.createPayment(appointmentId, patientId, amount, 
                                                        paymentMethod, phoneNumber, provider);

            return ResponseEntity.ok(payment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error creating payment: " + e.getMessage());
        }
    }

    /**
     * Process mobile money payment
     */
    @PostMapping("/{paymentId}/process")
    @PreAuthorize("hasAuthority('PATIENT')")
    public ResponseEntity<?> processMobileMoneyPayment(@PathVariable Long paymentId, 
                                                     @RequestBody Map<String, String> requestData) {
        try {
            String provider = requestData.get("provider");
            
            if (provider == null || (!provider.equals("ORANGE") && !provider.equals("MTN"))) {
                return ResponseEntity.badRequest().body("Invalid provider. Must be ORANGE or MTN");
            }

            Payment payment = paymentService.processMobileMoneyPayment(paymentId, provider);
            return ResponseEntity.ok(payment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error processing payment: " + e.getMessage());
        }
    }

    /**
     * Get payments by patient
     */
    @GetMapping("/patient/{patientId}")
    @PreAuthorize("hasAuthority('PATIENT')")
    public ResponseEntity<List<Payment>> getPaymentsByPatient(@PathVariable Long patientId) {
        List<Payment> payments = paymentService.getPaymentsByPatient(patientId);
        return ResponseEntity.ok(payments);
    }

    /**
     * Get payment by ID
     */
    @GetMapping("/{paymentId}")
    public ResponseEntity<?> getPaymentById(@PathVariable Long paymentId) {
        Optional<Payment> payment = paymentService.getPaymentById(paymentId);
        
        if (payment.isPresent()) {
            return ResponseEntity.ok(payment.get());
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Payment not found");
        }
    }

    /**
     * Update payment status
     */
    @PutMapping("/{paymentId}/status")
    @PreAuthorize("hasAuthority('ADMIN')")
    public ResponseEntity<?> updatePaymentStatus(@PathVariable Long paymentId, 
                                               @RequestBody Map<String, String> requestData) {
        try {
            String statusStr = requestData.get("status");
            PaymentStatus status = PaymentStatus.valueOf(statusStr);
            
            Payment payment = paymentService.updatePaymentStatus(paymentId, status);
            return ResponseEntity.ok(payment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error updating payment status: " + e.getMessage());
        }
    }

    /**
     * Get available payment methods
     */
    @GetMapping("/methods")
    public ResponseEntity<PaymentMethod[]> getPaymentMethods() {
        return ResponseEntity.ok(PaymentMethod.values());
    }

    /**
     * Get payment providers (ORANGE, MTN)
     */
    @GetMapping("/providers")
    public ResponseEntity<String[]> getPaymentProviders() {
        String[] providers = {"ORANGE", "MTN"};
        return ResponseEntity.ok(providers);
    }
} 