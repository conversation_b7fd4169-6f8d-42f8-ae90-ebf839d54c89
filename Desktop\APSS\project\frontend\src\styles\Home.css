.home-container {
  text-align: center;
  padding: 50px;
}

.home-header {
  background: #36A2EB;
  color: white;
  padding: 50px;
  border-radius: 10px;
}

.btn-primary {
  background-color: #ff9800;
  color: white;
  padding: 10px 20px;
  text-decoration: none;
  border-radius: 5px;
  display: inline-block;
  margin-top: 15px;
}

.home-services {
  margin-top: 40px;
}

.services-list {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.service-card {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  width: 250px;
}
