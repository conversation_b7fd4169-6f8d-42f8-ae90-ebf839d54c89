<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/project/src/main/java" isTestSource="false" />
    </content>
    <content url="file://$MODULE_DIR$/out" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:6.2.3" level="project" />
    <orderEntry type="module-library">
      <library name="testng">
        <CLASSES>
          <root url="jar://$MAVEN_REPOSITORY$/org/testng/testng/7.1.0/testng-7.1.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/beust/jcommander/1.72/jcommander-1.72.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/google/inject/guice/4.1.0/guice-4.1.0-no_aop.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/javax/inject/javax.inject/1/javax.inject-1.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/aopalliance/aopalliance/1.0/aopalliance-1.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/google/guava/guava/19.0/guava-19.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/org/yaml/snakeyaml/1.21/snakeyaml-1.21.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="testng">
        <CLASSES>
          <root url="jar://$MAVEN_REPOSITORY$/org/testng/testng/7.1.0/testng-7.1.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/beust/jcommander/1.72/jcommander-1.72.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/google/inject/guice/4.1.0/guice-4.1.0-no_aop.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/javax/inject/javax.inject/1/javax.inject-1.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/aopalliance/aopalliance/1.0/aopalliance-1.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/com/google/guava/guava/19.0/guava-19.0.jar!/" />
          <root url="jar://$MAVEN_REPOSITORY$/org/yaml/snakeyaml/1.21/snakeyaml-1.21.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="library" name="Maven: org.springframework:spring-web:6.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-core:6.4.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:6.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:6.4.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:6.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-jpa:3.4.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:10.1.36" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-web:6.4.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:6.2.3" level="project" />
    <orderEntry type="library" name="Maven: io.jsonwebtoken:jjwt-api:0.11.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-config:6.4.3" level="project" />
  </component>
</module>