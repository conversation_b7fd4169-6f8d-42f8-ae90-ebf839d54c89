import React from "react";
import { Routes, Route } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";
import AdminDashboard from "../pages/admin/Dashboard";
import ManageUsers from "../pages/admin/ManageUsers";
import ManageDoctors from "../pages/admin/ManageDoctors";
import ManageAppointments from "../pages/admin/ManageAppointments";
import ManagePayments from "../pages/admin/ManagePayments";
import Reports from "../pages/admin/Reports";
import SystemSettings from "../pages/admin/SystemSettings";
import Notifications from "../pages/admin/Notifications";
import FeedbackManagement from "../pages/admin/FeedBackManagement";

const AdminRoutes = ({ isAuthenticated }) => {
  return (
    <Routes>
      <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} allowedRoles={["admin"]} />}>
        <Route path="/admin/dashboard" element={<Dashboard />} />
        <Route path="/admin/users" element={<ManageUsers />} />
        <Route path="/admin/doctors" element={<ManageDoctors />} />
        <Route path="/admin/appointments" element={<ManageAppointments />} />
        <Route path="/admin/payments" element={<ManagePayments />} />
        <Route path="/admin/reports" element={<Reports />} />
        <Route path="/admin/settings" element={<SystemSettings />} />
        <Route path="/admin/notifications" element={<Notifications />} />
        <Route path="/admin/feedback" element={<FeedBackManagement />} />
      </Route>
    </Routes>
  );
};

export default AdminRoutes;
