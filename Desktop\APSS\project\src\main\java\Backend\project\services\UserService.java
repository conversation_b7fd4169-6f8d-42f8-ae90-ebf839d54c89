package Backend.project.services;

import Backend.project.model.User;
import Backend.project.repositories.UserRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {
    private final UserRepository userRepository;

    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    public List<User> getAllUser() {
        return userRepository.findAll();
    }

    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    public User createUser(User user) {
        return userRepository.save(user); // Corrected this line
    }

    public User updateUser(Long id, User user) {
        user.setId(id);
        return userRepository.save(user); // Corrected this line
    }

    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
}
