/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-size: var(--font-size-medium);
  transition: 0.3s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Cards */
.card {
  background: white;
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
}

/* Modals */
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
