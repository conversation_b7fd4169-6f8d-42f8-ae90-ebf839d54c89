import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spin<PERSON>, ListGroup, Badge } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faPaperPlane, faUserMd, faClock, faCheck, faCheckDouble } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

const Message = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchDoctors();
    fetchMessages();
  }, []);

  const fetchDoctors = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/doctors`);
      setDoctors(response.data || []);
    } catch (err) {
      console.error("Error fetching doctors:", err);
      // Mock data for demo
      setDoctors([
        { id: 1, name: "Dr. Smith", specialty: { name: "Cardiology" } },
        { id: 2, name: "Dr. Johnson", specialty: { name: "Neurology" } },
        { id: 3, name: "Dr. Brown", specialty: { name: "Pediatrics" } }
      ]);
    }
  };

  const fetchMessages = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/messages/patient/${user.id}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      setMessages(response.data || []);
    } catch (err) {
      console.error("Error fetching messages:", err);
      setError("Failed to load messages");
      // Mock data for demo
      setMessages([
        {
          id: 1,
          senderId: 1,
          senderName: "Dr. Smith",
          receiverId: user.id,
          receiverName: user.name,
          message: "Hello! I've reviewed your test results. Everything looks good.",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isRead: true,
          senderType: "DOCTOR"
        },
        {
          id: 2,
          senderId: user.id,
          senderName: user.name,
          receiverId: 1,
          receiverName: "Dr. Smith",
          message: "Thank you doctor. When should I schedule my next appointment?",
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          isRead: true,
          senderType: "PATIENT"
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!selectedDoctor || !newMessage.trim()) return;

    setSending(true);
    try {
      const messageData = {
        senderId: user.id,
        receiverId: selectedDoctor.id,
        message: newMessage.trim(),
        senderType: "PATIENT",
        receiverType: "DOCTOR"
      };

      const response = await axios.post(`${API_BASE_URL}/api/messages`, messageData, {
        headers: { Authorization: `Bearer ${user.token}` }
      });

      // Add the new message to the list
      setMessages([...messages, response.data]);
      setNewMessage("");
    } catch (err) {
      console.error("Error sending message:", err);
      setError("Failed to send message. Please try again.");
    } finally {
      setSending(false);
    }
  };

  const handleDoctorSelect = (doctor) => {
    setSelectedDoctor(doctor);
    setError("");
  };

  const getMessageStatus = (message) => {
    if (message.senderType === "PATIENT") {
      return message.isRead ? (
        <FontAwesomeIcon icon={faCheckDouble} className="text-primary" />
      ) : (
        <FontAwesomeIcon icon={faCheck} className="text-muted" />
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading messages...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FontAwesomeIcon icon={faEnvelope} className="me-2" />
            Messages
          </h2>
          <p className="text-muted">Communicate with your healthcare providers</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Row>
        <Col lg={4}>
          <Card className="shadow-sm mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faUserMd} className="me-2" />
                Select Doctor
              </h5>
            </Card.Header>
            <Card.Body>
              <ListGroup>
                {doctors.map((doctor) => (
                  <ListGroup.Item
                    key={doctor.id}
                    action
                    active={selectedDoctor?.id === doctor.id}
                    onClick={() => handleDoctorSelect(doctor)}
                    className="d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <strong>{doctor.name}</strong>
                      <div className="text-muted small">{doctor.specialty?.name}</div>
                    </div>
                    {selectedDoctor?.id === doctor.id && (
                      <Badge bg="primary">Selected</Badge>
                    )}
                  </ListGroup.Item>
                ))}
              </ListGroup>
            </Card.Body>
          </Card>

          {selectedDoctor && (
            <Card className="shadow-sm">
              <Card.Header>
                <h6 className="mb-0">Send Message</h6>
              </Card.Header>
              <Card.Body>
                <Form onSubmit={handleSendMessage}>
                  <Form.Group className="mb-3">
                    <Form.Label>To: {selectedDoctor.name}</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message here..."
                      disabled={sending}
                    />
                  </Form.Group>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={!newMessage.trim() || sending}
                    className="w-100"
                  >
                    {sending ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Sending...
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faPaperPlane} className="me-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          )}
        </Col>

        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                Message History
                {selectedDoctor && (
                  <span className="text-muted ms-2">- {selectedDoctor.name}</span>
                )}
              </h5>
            </Card.Header>
            <Card.Body style={{ height: "500px", overflowY: "auto" }}>
              {selectedDoctor ? (
                messages.length > 0 ? (
                  <div>
                    {messages
                      .filter(msg => 
                        (msg.senderId === user.id && msg.receiverId === selectedDoctor.id) ||
                        (msg.senderId === selectedDoctor.id && msg.receiverId === user.id)
                      )
                      .map((message) => (
                        <div
                          key={message.id}
                          className={`mb-3 ${
                            message.senderType === "PATIENT" ? "text-end" : "text-start"
                          }`}
                        >
                          <div
                            className={`d-inline-block p-3 rounded ${
                              message.senderType === "PATIENT"
                                ? "bg-primary text-white"
                                : "bg-light"
                            }`}
                            style={{ maxWidth: "70%" }}
                          >
                            <div className="mb-1">
                              <small className="text-muted">
                                <FontAwesomeIcon icon={faClock} className="me-1" />
                                {moment(message.timestamp).format("MMM D, h:mm A")}
                              </small>
                            </div>
                            <div>{message.message}</div>
                            {getMessageStatus(message)}
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-5">
                    <FontAwesomeIcon icon={faEnvelope} className="text-muted fa-3x mb-3" />
                    <h5>No messages yet</h5>
                    <p className="text-muted">Start a conversation with your doctor</p>
                  </div>
                )
              ) : (
                <div className="text-center py-5">
                  <FontAwesomeIcon icon={faUserMd} className="text-muted fa-3x mb-3" />
                  <h5>Select a doctor</h5>
                  <p className="text-muted">Choose a doctor to view your message history</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Message;
