import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Form, But<PERSON>, Alert, <PERSON>, <PERSON> } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { USER_ROLES } from "../../assets/utils/constants";
import { loginUser } from "../../assets/utils/api";
import "../../styles/form.css";

const Login = () => {
  const [formData, setFormData] = useState({ email: "", password: "" });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const isMounted = useRef(true);
  
  // Set up cleanup to prevent memory leaks
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const userData = await loginUser({
        email: formData.email,
        password: formData.password
      });
      
      // Save user data to context and localStorage
      login(userData);
      
      // Redirect based on user role
      if (userData.role.toUpperCase() === USER_ROLES.ADMIN.toUpperCase()) {
        navigate("/admin/dashboard");
      } else if (userData.role.toUpperCase() === USER_ROLES.DOCTOR.toUpperCase()) {
        navigate("/doctor/dashboard");
      } else if (userData.role.toUpperCase() === USER_ROLES.PATIENT.toUpperCase()) {
        navigate("/patient/dashboard");
      } else {
        navigate("/");
      }
    } catch (err) {
      console.error("Login error:", err);
      if (isMounted.current) {
        if (err.response && err.response.data) {
          setError(err.response.data);
        } else {
          setError("Login failed. Please check your credentials and try again.");
        }
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col md={6} lg={5}>
          <Card className="shadow-sm border-0">
            <Card.Body className="p-4">
              <div className="text-center mb-4">
                <h2 className="fw-bold">Welcome Back</h2>
                <p className="text-muted">Sign in to your account</p>
              </div>
              
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>Email address</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email"
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-4">
                  <Form.Label>Password</Form.Label>
                  <Form.Control
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="Enter your password"
                    required
                  />
                </Form.Group>




                <Button 
                  variant="primary" 
                  type="submit" 
                  disabled={loading}
                  className="w-100 py-2 mb-3"
                >
                  {loading ? (
                    <>
                      <Spinner
                        as="span"
                        animation="border"
                        size="sm"
                        role="status"
                        aria-hidden="true"
                        className="me-2"
                      />
                      Signing in...
                    </>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </Form>

               <div className="text-center mt-2">
                                <Link to="/forgot-password" className="text-primary">Forgot Password?</Link>
                              </div>
              
              <div className="text-center mt-3">
                <p className="mb-0">
                  Don't have an account? <Link to="/register" className="text-primary">Sign Up</Link>
                </p>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Login;
