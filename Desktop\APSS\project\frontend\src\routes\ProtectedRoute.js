import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { USER_ROLES } from "../assets/utils/constants";

const ProtectedRoute = ({ role, children }) => {
  const { user } = useAuth();

  if (!user) {
    return <Navigate to="/login" />;
  }

  // Fix: Compare roles case-insensitively
  const userRole = user.role.toLowerCase();
  const requiredRole = role.toLowerCase();

  if (userRole !== requiredRole) {
    // Redirect to appropriate dashboard based on user role
    if (userRole === USER_ROLES.ADMIN) {
      return <Navigate to="/admin/dashboard" />;
    } else if (userRole === USER_ROLES.DOCTOR) {
      return <Navigate to="/doctor/dashboard" />;
    } else if (userRole === USER_ROLES.PATIENT) {
      return <Navigate to="/patient/dashboard" />;
    } else {
      return <Navigate to="/" />;
    }
  }

  return children;
};

export default ProtectedRoute;
