import React, { useEffect, useState } from "react";
import { Card, Container, Row, Col, Table, Badge, Button, Tabs, Tab } from "react-bootstrap";
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCalendarAlt, faUserMd, faChartLine, faClipboardCheck, faClock } from "@fortawesome/free-solid-svg-icons";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { getDoctorAppointments, getDoctorUpcomingAppointments, confirmAppointment, cancelAppointment, getDoctorStats } from "../../assets/utils/api";

// Add icons to library
library.add(faCalendarAlt, faUserMd, faChartLine, faClipboardCheck, faClock);

const localizer = momentLocalizer(moment);

const DoctorDashboard = () => {
  const [doctor, setDoctor] = useState({});
  const [todayAppointments, setTodayAppointments] = useState([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [stats, setStats] = useState({
    totalPatients: 0,
    totalAppointments: 0,
    pendingAppointments: 0,
    completedAppointments: 0
  });

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (user) {
      setDoctor(user);
      fetchDoctorData(user.id);
    }
  }, []);

  const fetchDoctorData = async (doctorId) => {
    try {
      // Fetch today's appointments
      const todayRes = await getDoctorAppointments(doctorId);
      setTodayAppointments(todayRes.data);

      // Fetch upcoming appointments
      const upcomingRes = await getDoctorUpcomingAppointments(doctorId);
      setUpcomingAppointments(upcomingRes.data);

      // Transform appointments for calendar view
      const events = upcomingRes.data.map(appt => ({
        id: appt.id,
        title: `${appt.patientName} - ${appt.reason}`,
        start: new Date(appt.appointmentDate),
        end: moment(appt.appointmentDate).add(30, 'minutes').toDate(),
        status: appt.status
      }));
      setCalendarEvents(events);

      // Fetch doctor stats
      const statsRes = await getDoctorStats(doctorId);
      setStats({
        totalPatients: statsRes.data.totalPatients,
        totalAppointments: statsRes.data.totalAppointments,
        pendingAppointments: statsRes.data.pendingAppointments,
        completedAppointments: statsRes.data.completedAppointments
      });
    } catch (error) {
      console.error("Error fetching doctor data:", error);
    }
  };

  const handleAppointmentAction = async (appointmentId, action) => {
    try {
      if (action === 'confirm') {
        await confirmAppointment(appointmentId);
      } else if (action === 'cancel') {
        await cancelAppointment(appointmentId);
      }

      // Refresh the data after successful action
      const user = JSON.parse(localStorage.getItem("user"));
      await fetchDoctorData(user.id);

      // Show success message
      alert(`Appointment ${action === 'confirm' ? 'confirmed' : 'canceled'} successfully`);
    } catch (error) {
      console.error(`Error ${action}ing appointment:`, error);
      alert(`Failed to ${action} appointment. Please try again.`);
    }
  };

  const getStatusBadge = (status) => {
    switch(status) {
      case 'PENDING':
        return <Badge bg="warning">Pending</Badge>;
      case 'CONFIRMED':
        return <Badge bg="success">Confirmed</Badge>;
      case 'CANCELED':
        return <Badge bg="danger">Canceled</Badge>;
      case 'COMPLETED':
        return <Badge bg="info">Completed</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col md={8}>
          <h2>Welcome, Dr. {doctor.name}</h2>
          <p className="text-muted">Here's your dashboard for {moment().format('MMMM D, YYYY')}</p>
        </Col>
        <Col md={4} className="d-flex justify-content-md-end align-items-center">
          <Button 
            variant="outline-primary" 
            size="lg"
            href="/doctor/availability"
            className="d-flex align-items-center"
          >
            <FontAwesomeIcon icon={faClock} className="me-2" />
            Manage Availability
          </Button>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center h-100 shadow-sm">
            <Card.Body>
              <h3 className="display-4">{stats.totalPatients}</h3>
              <Card.Title>Total Patients</Card.Title>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100 shadow-sm">
            <Card.Body>
              <h3 className="display-4">{stats.totalAppointments}</h3>
              <Card.Title>Total Appointments</Card.Title>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100 shadow-sm">
            <Card.Body>
              <h3 className="display-4">{stats.pendingAppointments}</h3>
              <Card.Title>Pending Appointments</Card.Title>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100 shadow-sm">
            <Card.Body>
              <h3 className="display-4">{stats.completedAppointments}</h3>
              <Card.Title>Completed Appointments</Card.Title>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card className="shadow-sm">
            <Card.Body>
              <Tabs defaultActiveKey="today" className="mb-3">
                <Tab eventKey="today" title="Today's Appointments">
                  {todayAppointments.length > 0 ? (
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>Time</th>
                          <th>Patient</th>
                          <th>Reason</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {todayAppointments.map((appt) => (
                          <tr key={appt.id}>
                            <td>{moment(appt.appointmentDate).format('h:mm A')}</td>
                            <td>{appt.patientName}</td>
                            <td>{appt.reason}</td>
                            <td>{getStatusBadge(appt.status)}</td>
                            <td>
                              {appt.status === 'PENDING' && (
                                <>
                                  <Button 
                                    variant="success" 
                                    size="sm" 
                                    className="me-2"
                                    onClick={() => handleAppointmentAction(appt.id, 'confirm')}
                                  >
                                    Confirm
                                  </Button>
                                  <Button 
                                    variant="danger" 
                                    size="sm"
                                    onClick={() => handleAppointmentAction(appt.id, 'cancel')}
                                  >
                                    Cancel
                                  </Button>
                                </>
                              )}
                              {appt.status === 'CONFIRMED' && (
                                <Button 
                                  variant="primary" 
                                  size="sm"
                                  onClick={() => window.location.href = `/appointments/${appt.id}/details`}
                                >
                                  View Details
                                </Button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <p className="text-center my-4">No appointments scheduled for today.</p>
                  )}
                </Tab>
                <Tab eventKey="upcoming" title="Upcoming Appointments">
                  {upcomingAppointments.length > 0 ? (
                    <Table responsive hover>
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Time</th>
                          <th>Patient</th>
                          <th>Reason</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {upcomingAppointments.map((appt) => (
                          <tr key={appt.id}>
                            <td>{moment(appt.appointmentDate).format('MMM D, YYYY')}</td>
                            <td>{moment(appt.appointmentDate).format('h:mm A')}</td>
                            <td>{appt.patientName}</td>
                            <td>{appt.reason}</td>
                            <td>{getStatusBadge(appt.status)}</td>
                            <td>
                              {appt.status === 'PENDING' && (
                                <>
                                  <Button 
                                    variant="success" 
                                    size="sm" 
                                    className="me-2"
                                    onClick={() => handleAppointmentAction(appt.id, 'confirm')}
                                  >
                                    Confirm
                                  </Button>
                                  <Button 
                                    variant="danger" 
                                    size="sm"
                                    onClick={() => handleAppointmentAction(appt.id, 'cancel')}
                                  >
                                    Cancel
                                  </Button>
                                </>
                              )}
                              {appt.status === 'CONFIRMED' && (
                                <Button 
                                  variant="primary" 
                                  size="sm"
                                  onClick={() => window.location.href = `/appointments/${appt.id}/details`}
                                >
                                  View Details
                                </Button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <p className="text-center my-4">No upcoming appointments.</p>
                  )}
                </Tab>
                <Tab eventKey="calendar" title="Calendar View">
                  <div style={{ height: 500 }}>
                    <Calendar
                      localizer={localizer}
                      events={calendarEvents}
                      startAccessor="start"
                      endAccessor="end"
                      style={{ height: "100%" }}
                      eventPropGetter={(event) => {
                        let backgroundColor = '#3174ad';
                        if (event.status === 'CONFIRMED') backgroundColor = '#28a745';
                        if (event.status === 'CANCELED') backgroundColor = '#dc3545';
                        return { style: { backgroundColor } };
                      }}
                    />
                  </div>
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default DoctorDashboard;