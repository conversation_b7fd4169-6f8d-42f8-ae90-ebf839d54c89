package Backend.project.controller;

import Backend.project.model.Appointment;
import Backend.project.model.AppointmentStatus;
import Backend.project.model.Doctor;
import Backend.project.model.Patient;
import Backend.project.repositories.AppointmentRepository;
import Backend.project.repositories.DoctorRepository;
import Backend.project.repositories.PatientRepository;
import Backend.project.repositories.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "http://localhost:3000")
public class DashboardController {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @GetMapping("/admin/stats")
    public ResponseEntity<?> getAdminStats() {
        logger.info("Fetching admin dashboard statistics");
        
        try {
            // Get counts from repositories
            long totalUsers = userRepository.count();
            long totalDoctors = userRepository.countByRole("DOCTOR");
            long totalPatients = userRepository.countByRole("PATIENT");
            long totalAppointments = appointmentRepository.count();
            
            // Get appointments by status
            List<Appointment> appointments = appointmentRepository.findAll();
            long pendingAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.PENDING.equals(a.getStatus()))
                    .count();
            long completedAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.COMPLETED.equals(a.getStatus()))
                    .count();
            long canceledAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.CANCELED.equals(a.getStatus()))
                    .count();
            
            // Create response map
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", totalUsers);
            stats.put("totalDoctors", totalDoctors);
            stats.put("totalPatients", totalPatients);
            stats.put("totalAppointments", totalAppointments);
            stats.put("pendingAppointments", pendingAppointments);
            stats.put("completedAppointments", completedAppointments);
            stats.put("canceledAppointments", canceledAppointments);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error fetching admin stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Failed to fetch dashboard statistics");
        }
    }
    
    @GetMapping("/doctor/{id}/stats")
    public ResponseEntity<?> getDoctorStats(@PathVariable Long id) {
        logger.info("Fetching dashboard statistics for doctor ID: {}", id);
        
        try {
            // Check if doctor exists
            Doctor doctor = doctorRepository.findById(id).orElse(null);
            if (doctor == null) {
                return ResponseEntity.badRequest().body("Doctor not found");
            }
            
            // Get appointments for this doctor
            List<Appointment> appointments = appointmentRepository.findByDoctor(doctor);
            
            // Calculate stats
            long totalAppointments = appointments.size();
            long pendingAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.PENDING.equals(a.getStatus()))
                    .count();
            long completedAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.COMPLETED.equals(a.getStatus()))
                    .count();
            long canceledAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.CANCELED.equals(a.getStatus()))
                    .count();
            
            // Create response map
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalAppointments", totalAppointments);
            stats.put("pendingAppointments", pendingAppointments);
            stats.put("completedAppointments", completedAppointments);
            stats.put("canceledAppointments", canceledAppointments);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error fetching doctor stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Failed to fetch dashboard statistics");
        }
    }
    
    @GetMapping("/patient/{id}/stats")
    public ResponseEntity<?> getPatientStats(@PathVariable Long id) {
        logger.info("Fetching dashboard statistics for patient ID: {}", id);
        
        try {
            // Check if patient exists
            Patient patient = patientRepository.findById(id).orElse(null);
            if (patient == null) {
                return ResponseEntity.badRequest().body("Patient not found");
            }
            
            // Get appointments for this patient
            List<Appointment> appointments = appointmentRepository.findByPatient(patient);
            
            // Calculate stats
            long totalAppointments = appointments.size();
            long pendingAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.PENDING.equals(a.getStatus()))
                    .count();
            long completedAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.COMPLETED.equals(a.getStatus()))
                    .count();
            long canceledAppointments = appointments.stream()
                    .filter(a -> AppointmentStatus.CANCELED.equals(a.getStatus()))
                    .count();
            
            // Create response map
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalAppointments", totalAppointments);
            stats.put("pendingAppointments", pendingAppointments);
            stats.put("completedAppointments", completedAppointments);
            stats.put("canceledAppointments", canceledAppointments);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error fetching patient stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Failed to fetch dashboard statistics");
        }
    }
}
