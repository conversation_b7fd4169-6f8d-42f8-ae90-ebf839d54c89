package Backend.project.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

@Service
public class EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    @Autowired(required = false)
    private JavaMailSender mailSender;
    
    @Value("${app.email.from:<EMAIL>}")
    private String fromEmail;
    
    @Value("${app.email.enabled:false}")
    private boolean emailEnabled;
    
    /**
     * Send a simple email
     */
    @Async
    public void sendEmail(String to, String subject, String text) {
        try {
            if (emailEnabled && mailSender != null) {
                // Send actual email using JavaMailSender
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(fromEmail);
                message.setTo(to);
                message.setSubject(subject);
                message.setText(text);
                mailSender.send(message);
                logger.info("Email sent successfully to: {}", to);
            } else {
                // Fallback to simulation mode if email is not enabled or mailSender is not configured
                logger.info("Email sending is disabled or not configured. Simulating email to: {}", to);
                logger.info("From: {}", fromEmail);
                logger.info("To: {}", to);
                logger.info("Subject: {}", subject);
                logger.info("Content: {}", text);
                logger.info("Email simulation completed successfully");
            }
        } catch (Exception e) {
            logger.error("Failed to send email: {}", e.getMessage());
        }
    }
    
    /**
     * Send password reset email
     */
    @Async
    public void sendPasswordResetEmail(String to, String token, String baseUrl) {
        String subject = "Password Reset Request";
        String resetUrl = baseUrl + "/reset-password?token=" + token;
        String message = "Hello,\n\n" +
                "You have requested to reset your password. Please click the link below to reset your password:\n\n" +
                resetUrl + "\n\n" +
                "If you did not request a password reset, please ignore this email.\n\n" +
                "Regards,\nMediAppoint Team";
        
        sendEmail(to, subject, message);
    }
    
    /**
     * Send HTML email
     */
    @Async
    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        try {
            if (emailEnabled && mailSender != null) {
                // Send actual HTML email using JavaMailSender
                MimeMessage mimeMessage = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
                helper.setFrom(fromEmail);
                helper.setTo(to);
                helper.setSubject(subject);
                helper.setText(htmlContent, true); // true indicates HTML content
                mailSender.send(mimeMessage);
                logger.info("HTML email sent successfully to: {}", to);
            } else {
                // Fallback to simulation mode
                logger.info("HTML email sending is disabled or not configured. Simulating email to: {}", to);
                logger.info("From: {}", fromEmail);
                logger.info("To: {}", to);
                logger.info("Subject: {}", subject);
                logger.info("HTML Content: {}", htmlContent);
                logger.info("HTML email simulation completed successfully");
            }
        } catch (MessagingException e) {
            logger.error("Failed to send HTML email: {}", e.getMessage());
        }
    }
    
    /**
     * Handle contact form submission
     * Sends an email to the admin and a confirmation email to the user
     */
    @Async
    public void handleContactFormSubmission(String name, String email, String subject, String message, String adminEmail) {
        // Send notification to admin
        String adminSubject = "New Contact Form Submission: " + subject;
        String adminMessage = String.format(
            "<html><body>" +
            "<h2>New Contact Form Submission</h2>" +
            "<p><strong>From:</strong> %s (%s)</p>" +
            "<p><strong>Subject:</strong> %s</p>" +
            "<p><strong>Message:</strong></p>" +
            "<div style='background-color: #f5f5f5; padding: 15px; border-radius: 5px;'>%s</div>" +
            "<p>Please respond to this inquiry at your earliest convenience.</p>" +
            "</body></html>",
            name, email, subject, message.replace("\n", "<br>"));
        
        sendHtmlEmail(adminEmail, adminSubject, adminMessage);
        logger.info("Contact form notification sent to admin: {}", adminEmail);
        
        // Send confirmation to user
        String userSubject = "Thank you for contacting MediAppoint";
        String userMessage = String.format(
            "<html><body>" +
            "<h2>Thank You for Contacting Us</h2>" +
            "<p>Dear %s,</p>" +
            "<p>We have received your message regarding <strong>\"%s\"</strong>.</p>" +
            "<p>Our team will review your inquiry and get back to you as soon as possible. Please allow 24-48 hours for a response.</p>" +
            "<p>For your reference, here is a copy of your message:</p>" +
            "<div style='background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;'>%s</div>" +
            "<p>Best regards,<br>The MediAppoint Team</p>" +
            "</body></html>",
            name, subject, message.replace("\n", "<br>"));
        
        sendHtmlEmail(email, userSubject, userMessage);
        logger.info("Contact form confirmation sent to user: {}", email);
    }
}
