import React from "react";
import { Routes, Route } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";
import Dashboard from "../pages/doctor/Dashboard";
import Profile from "../pages/doctor/Profile";
import ScheduleAppointments from "../pages/doctor/ScheduleAppointments";
import PatientList from "../pages/doctor/PatientList";
import Prescriptions from "../pages/doctor/Prescriptions";
import Reports from "../pages/doctor/Reports";
import Earnings from "../pages/doctor/Earnings";
import Messages from "../pages/doctor/Messages";
import Notifications from "../pages/doctor/Notifications";

const DoctorRoutes = ({ isAuthenticated }) => {
  return (
    <Routes>
      <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} allowedRoles={["doctor"]} />}>
        <Route path="/doctor/dashboard" element={<Dashboard />} />
        <Route path="/doctor/profile" element={<Profile />} />
        <Route path="/doctor/schedule" element={<ScheduleAppointments />} />
        <Route path="/doctor/patients" element={<PatientList />} />
        <Route path="/doctor/prescriptions" element={<Prescriptions />} />
        <Route path="/doctor/reports" element={<Reports />} />
        <Route path="/doctor/earnings" element={<Earnings />} />
        <Route path="/doctor/messages" element={<Messages />} />
        <Route path="/doctor/notifications" element={<Notifications />} />
      </Route>
    </Routes>
  );
};

export default DoctorRoutes;
