import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Form, InputGroup } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faFilter } from "@fortawesome/free-solid-svg-icons";
import { getAllDoctors } from "../../assets/utils/api";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

// Import doctor images
import doctor1 from '../../assets/images/41808433_l.jpg';
import doctor2 from '../../assets/images/What-is-the-Role-of-a-Primary-Care-Physician.jpg';
import doctor3 from '../../assets/images/young-asian-female-dentist-white-coat-posing-clinic-equipment.jpg';
import doctor4 from '../../assets/images/about-doctor-speaking-with-medical-team.jpg';
import doctor5 from '../../assets/images/female-doctor-with-presenting-hand-gesture.jpg';
import doctor6 from '../../assets/images/portrait-successful-mid-adult-doctor-with-crossed-arms.jpg';

const doctorImages = [doctor1, doctor2, doctor3, doctor4, doctor5, doctor6];

const DoctorList = () => {
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const [specialties, setSpecialties] = useState([]);
  
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Handle booking appointment - redirect to login if not authenticated
  const handleBookAppointment = (doctorId) => {
    if (!user) {
      // Not authenticated, redirect to login
      navigate('/login', { state: { from: `/patient/book-appointment?doctor=${doctorId}` } });
    } else if (user.role === 'PATIENT') {
      // Authenticated as patient, go to booking page
      navigate(`/patient/book-appointment?doctor=${doctorId}`);
    } else {
      // Authenticated but not as patient
      alert('You need a patient account to book appointments');
    }
  };

  useEffect(() => {
    const fetchDoctors = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch all doctors
        const response = await getAllDoctors();
        console.log('Doctors data from API:', response.data);
        
        if (response.data && Array.isArray(response.data)) {
          setDoctors(response.data);
          
          // Extract unique specialties from doctors data
          const uniqueSpecialties = [...new Set(response.data.map(doctor => doctor.specialty?.name))].filter(Boolean);
          setSpecialties(uniqueSpecialties);
        } else {
          console.error('Invalid doctors data format:', response.data);
          setError('Failed to load doctors data. Please try again later.');
          setDoctors([]);
        }
        
        // Fetch all specialties separately to ensure we have a complete list
        try {
          // Use the public endpoint instead of admin endpoint for specialties
          const specialtiesResponse = await axios.get(`${API_BASE_URL}/api/public/specialties`);
          console.log('Specialties data from API:', specialtiesResponse.data);
          
          if (specialtiesResponse.data && Array.isArray(specialtiesResponse.data)) {
            const allSpecialties = specialtiesResponse.data.map(specialty => specialty.name);
            setSpecialties(prevSpecialties => {
              // Combine with any specialties already extracted from doctors
              const combinedSpecialties = [...new Set([...prevSpecialties, ...allSpecialties])];
              return combinedSpecialties.filter(Boolean); // Remove any null/undefined values
            });
          }
        } catch (specialtyError) {
          console.error('Error fetching specialties:', specialtyError);
          // Don't set an error here as we already have specialties from doctors
        }
      } catch (error) {
        console.error('Error fetching doctors:', error);
        setError('Failed to load doctors. Please try again later.');
        setDoctors([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDoctors();
  }, []);

  // Filter doctors based on search term and selected specialty
  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch = doctor.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         doctor.specialty?.name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSpecialty = selectedSpecialty === "" || doctor.specialty?.name === selectedSpecialty;
    
    return matchesSearch && matchesSpecialty;
  });

  return (
    <div className="page-container pt-5 mt-5">
      <Container>
        <h1 className="text-center mb-2 mt-4">Find a Doctor</h1>
        <p className="text-center text-muted mb-5">Browse our network of qualified healthcare professionals</p>
        
        {/* Search and Filter */}
        <Row className="mb-5">
          <Col md={8}>
            <InputGroup className="mb-3">
              <InputGroup.Text>
                <FontAwesomeIcon icon={faSearch} />
              </InputGroup.Text>
              <Form.Control
                placeholder="Search by name or specialty..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </Col>
          <Col md={4}>
            <Form.Select 
              value={selectedSpecialty} 
              onChange={(e) => setSelectedSpecialty(e.target.value)}
            >
              <option value="">All Specialties</option>
              {specialties.map((specialty, index) => (
                <option key={index} value={specialty}>
                  {specialty}
                </option>
              ))}
            </Form.Select>
          </Col>
        </Row>
        
        {/* Doctors List */}
        {loading ? (
          <div className="text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading doctors...</p>
          </div>
        ) : error && doctors.length === 0 ? (
          <div className="alert alert-info text-center" role="alert">
            {error}
          </div>
        ) : filteredDoctors.length === 0 ? (
          <div className="alert alert-info text-center" role="alert">
            No doctors found matching your criteria. Please try a different search.
          </div>
        ) : (
          <Row className="g-4">
            {filteredDoctors.map((doctor, index) => (
              <Col md={4} className="mb-4" key={doctor.id || index}>
                <Card className="h-100 border-0 shadow-sm doctor-card">
                  <div className="doctor-img-container">
                    <Card.Img
                      variant="top"
                      src={doctorImages[index % doctorImages.length]}
                      alt={`Dr. ${doctor.name}`}
                      className="doctor-img"
                    />
                  </div>
                  <Card.Body className="text-center p-4">
                    <h4 className="card-title mb-1">
                      {(doctor.role === 'DOCTOR' || doctor.type === 'DOCTOR') ? `Dr. ${doctor.name}` : doctor.name}
                    </h4>
                    <p className="text-primary mb-3">{doctor.specialty?.name || "General Medicine"}</p>
                    <p className="text-muted mb-4">
                      {doctor.bio || "Experienced healthcare professional dedicated to providing quality patient care."}
                    </p>
                    <div className="d-flex justify-content-between">
                      <Button 
                        as={Link} 
                        to={`/doctors/${doctor.id}`} 
                        variant="outline-secondary" 
                        className="px-3"
                      >
                        View Profile
                      </Button>
                      <Button 
                        variant="primary" 
                        className="px-3"
                        onClick={() => handleBookAppointment(doctor.id)}
                      >
                        Book Appointment
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Container>
    </div>
  );
};

export default DoctorList;
