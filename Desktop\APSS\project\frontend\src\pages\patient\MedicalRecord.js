import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Accordion } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileMedical, faStethoscope, faPills, faThermometerHalf, faHeartbeat } from "@fortawesome/free-solid-svg-icons";
import moment from "moment";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import { useAuth } from "../../context/AuthContext";

const MedicalRecord = () => {
  const { user } = useAuth();
  const [medicalRecords, setMedicalRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchMedicalRecords();
  }, []);

  const fetchMedicalRecords = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/patients/${user.id}/medical-records`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      setMedicalRecords(response.data || []);
    } catch (err) {
      console.error("Error fetching medical records:", err);
      setError("Failed to load medical records");
      // Mock data for demo
      setMedicalRecords([
        {
          id: 1,
          date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Smith",
          specialty: "Cardiology",
          diagnosis: "Hypertension",
          prescription: "Amlodipine 5mg daily",
          notes: "Blood pressure elevated. Continue monitoring.",
          vitalSigns: {
            bloodPressure: "140/90",
            heartRate: "72 bpm",
            temperature: "98.6°F",
            weight: "75 kg"
          }
        },
        {
          id: 2,
          date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          doctorName: "Dr. Johnson",
          specialty: "General Medicine",
          diagnosis: "Common Cold",
          prescription: "Rest and fluids",
          notes: "Viral infection. Symptoms should resolve in 7-10 days.",
          vitalSigns: {
            bloodPressure: "120/80",
            heartRate: "68 bpm",
            temperature: "99.2°F",
            weight: "75 kg"
          }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getRecordTypeBadge = (type) => {
    switch (type) {
      case "CONSULTATION":
        return <Badge bg="primary">Consultation</Badge>;
      case "FOLLOW_UP":
        return <Badge bg="info">Follow-up</Badge>;
      case "EMERGENCY":
        return <Badge bg="danger">Emergency</Badge>;
      case "ROUTINE":
        return <Badge bg="success">Routine</Badge>;
      default:
        return <Badge bg="secondary">{type}</Badge>;
    }
  };

  if (loading) {
    return (
      <Container className="py-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3">Loading medical records...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="p-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FontAwesomeIcon icon={faFileMedical} className="me-2" />
            Medical Records
          </h2>
          <p className="text-muted">View your complete medical history and records</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" onClose={() => setError("")} dismissible>
          {error}
        </Alert>
      )}

      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faStethoscope} className="me-2" />
                Medical History
              </h5>
            </Card.Header>
            <Card.Body>
              {medicalRecords.length > 0 ? (
                <Accordion>
                  {medicalRecords.map((record, index) => (
                    <Accordion.Item key={record.id} eventKey={index.toString()}>
                      <Accordion.Header>
                        <div className="d-flex justify-content-between align-items-center w-100 me-3">
                          <div>
                            <strong>{moment(record.date).format("MMM D, YYYY")}</strong>
                            <span className="ms-3 text-muted">{record.doctorName}</span>
                          </div>
                          <div>
                            {getRecordTypeBadge(record.type || "CONSULTATION")}
                          </div>
                        </div>
                      </Accordion.Header>
                      <Accordion.Body>
                        <Row>
                          <Col md={6}>
                            <h6>Diagnosis</h6>
                            <p>{record.diagnosis}</p>
                            
                            <h6>Prescription</h6>
                            <p>{record.prescription}</p>
                            
                            <h6>Notes</h6>
                            <p>{record.notes}</p>
                          </Col>
                          <Col md={6}>
                            <h6>Vital Signs</h6>
                            {record.vitalSigns && (
                              <div>
                                <p><FontAwesomeIcon icon={faHeartbeat} className="me-2" />
                                  Blood Pressure: {record.vitalSigns.bloodPressure}
                                </p>
                                <p><FontAwesomeIcon icon={faHeartbeat} className="me-2" />
                                  Heart Rate: {record.vitalSigns.heartRate}
                                </p>
                                <p><FontAwesomeIcon icon={faThermometerHalf} className="me-2" />
                                  Temperature: {record.vitalSigns.temperature}
                                </p>
                                <p>Weight: {record.vitalSigns.weight}</p>
                              </div>
                            )}
                          </Col>
                        </Row>
                      </Accordion.Body>
                    </Accordion.Item>
                  ))}
                </Accordion>
              ) : (
                <div className="text-center py-5">
                  <FontAwesomeIcon icon={faFileMedical} className="text-muted fa-3x mb-3" />
                  <h5>No medical records found</h5>
                  <p className="text-muted">Your medical records will appear here after consultations</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm mb-4">
            <Card.Header>
              <h6 className="mb-0">
                <FontAwesomeIcon icon={faPills} className="me-2" />
                Current Medications
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>Amlodipine 5mg</strong>
                <p className="text-muted small mb-1">Daily - Blood pressure</p>
                <small className="text-muted">Started: 30 days ago</small>
              </div>
              <div className="mb-3">
                <strong>Vitamin D 1000 IU</strong>
                <p className="text-muted small mb-1">Daily - Supplement</p>
                <small className="text-muted">Started: 60 days ago</small>
              </div>
            </Card.Body>
          </Card>

          <Card className="shadow-sm">
            <Card.Header>
              <h6 className="mb-0">
                <FontAwesomeIcon icon={faHeartbeat} className="me-2" />
                Allergies
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="mb-2">
                <Badge bg="warning" className="me-2">Penicillin</Badge>
                <Badge bg="warning" className="me-2">Sulfa Drugs</Badge>
              </div>
              <p className="text-muted small">No known food allergies</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default MedicalRecord;
