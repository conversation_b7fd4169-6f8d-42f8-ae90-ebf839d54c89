package Backend.project.controller;

import Backend.project.model.DoctorAvailability;
import Backend.project.services.DoctorAvailabilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@RestController
@RequestMapping("/api/availability")
@CrossOrigin(origins = "http://localhost:3000")
public class DoctorAvailabilityController {

    @Autowired
    private DoctorAvailabilityService availabilityService;

    @GetMapping("/doctor/{doctorId}")
    public ResponseEntity<List<DoctorAvailability>> getDoctorAvailability(@PathVariable Long doctorId) {
        List<DoctorAvailability> availabilityList = availabilityService.getDoctorAvailability(doctorId);
        return ResponseEntity.ok(availabilityList);
    }

    @GetMapping("/doctor/{doctorId}/day/{dayOfWeek}")
    public ResponseEntity<List<DoctorAvailability>> getDoctorAvailabilityByDay(
            @PathVariable Long doctorId,
            @PathVariable DayOfWeek dayOfWeek) {
        List<DoctorAvailability> availabilityList = availabilityService.getDoctorAvailabilityByDay(doctorId, dayOfWeek);
        return ResponseEntity.ok(availabilityList);
    }

    @PostMapping("/doctor/{doctorId}")
    @PreAuthorize("hasAuthority('DOCTOR') or hasAuthority('ADMIN')")
    public ResponseEntity<?> addAvailabilitySlot(
            @PathVariable Long doctorId,
            @RequestParam DayOfWeek dayOfWeek,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime) {
        
        try {
            // Log the received parameters for debugging
            System.out.println("Adding availability slot with parameters:");
            System.out.println("doctorId: " + doctorId);
            System.out.println("dayOfWeek: " + dayOfWeek);
            System.out.println("startTime: " + startTime);
            System.out.println("endTime: " + endTime);
            
            // Validate times
            if (startTime.isAfter(endTime) || startTime.equals(endTime)) {
                return ResponseEntity.badRequest().body("Start time must be before end time");
            }
            
            try {
                // Try to add the availability slot
                DoctorAvailability newSlot = availabilityService.addAvailabilitySlot(doctorId, dayOfWeek, startTime, endTime);
                return ResponseEntity.ok(newSlot);
            } catch (IllegalArgumentException e) {
                // If doctor not found, try to create a doctor record for this user
                if (e.getMessage().contains("Doctor not found")) {
                    System.out.println("Doctor not found, attempting to create doctor record for user ID: " + doctorId);
                    
                    // For simplicity, we'll use the same ID for both user and doctor
                    // In a real application, you would fetch the user and create a proper doctor record
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body("Doctor profile not found. Please set up your doctor profile first.");
                } else {
                    return ResponseEntity.badRequest().body(e.getMessage());
                }
            }
        } catch (Exception e) {
            // Log the error for server-side debugging
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to add availability slot: " + e.getMessage());
        }
    }

    @PutMapping("/{availabilityId}")
    @PreAuthorize("hasAuthority('DOCTOR') or hasAuthority('ADMIN')")
    public ResponseEntity<DoctorAvailability> updateAvailabilitySlot(
            @PathVariable Long availabilityId,
            @RequestParam DayOfWeek dayOfWeek,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime,
            @RequestParam boolean isAvailable) {
        
        DoctorAvailability updatedSlot = availabilityService.updateAvailabilitySlot(
                availabilityId, dayOfWeek, startTime, endTime, isAvailable);
        return ResponseEntity.ok(updatedSlot);
    }

    @DeleteMapping("/{availabilityId}")
    @PreAuthorize("hasAuthority('DOCTOR') or hasAuthority('ADMIN')")
    public ResponseEntity<Void> deleteAvailabilitySlot(@PathVariable Long availabilityId) {
        availabilityService.deleteAvailabilitySlot(availabilityId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/check")
    public ResponseEntity<Boolean> checkAvailability(
            @RequestParam Long doctorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime) {
        
        boolean isAvailable = availabilityService.isDoctorAvailable(doctorId, date, startTime, endTime);
        return ResponseEntity.ok(isAvailable);
    }
}
