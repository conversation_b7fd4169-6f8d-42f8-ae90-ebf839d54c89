package Backend.project.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "Specialty")
public class Specialty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name; // e.g., Dermatology, Cardiology, etc.
    
    private String description; // Description of the specialty

    @OneToMany(mappedBy = "specialty", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<Doctor> doctors = new ArrayList<>(); // Initialize to avoid NullPointerException

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Doctor> getDoctors() {
        return doctors;
    }

    public void setDoctors(List<Doctor> doctors) {
        this.doctors = doctors;
    }

    // Convenience method to add a doctor
    public void addDoctor(Doctor doctor) {
        doctors.add(doctor);
        doctor.setSpecialty(this);
    }

    // Convenience method to remove a doctor
    public void removeDoctor(Doctor doctor) {
        doctors.remove(doctor);
        doctor.setSpecialty(null);
    }
}