package Backend.project.repositories;

import Backend.project.model.Payment;
import Backend.project.model.Patient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    List<Payment> findByPatient(Patient patient);
    List<Payment> findByPatientId(Long patientId);
    List<Payment> findByAppointmentId(Long appointmentId);
} 