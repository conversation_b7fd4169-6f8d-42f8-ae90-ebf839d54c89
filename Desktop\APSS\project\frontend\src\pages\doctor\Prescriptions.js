import React, { useState } from "react";
import "../styles/form.css";

const Prescriptions = () => {
  const [prescription, setPrescription] = useState({ patient: "", medicine: "", dosage: "" });

  const handleChange = (e) => {
    setPrescription({ ...prescription, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert("Prescription saved successfully!");
  };

  return (
    <div className="prescriptions">
      <h1>Manage Prescriptions</h1>
      <form onSubmit={handleSubmit}>
        <input type="text" name="patient" placeholder="Patient Name" onChange={handleChange} required />
        <input type="text" name="medicine" placeholder="Medicine" onChange={handleChange} required />
        <input type="text" name="dosage" placeholder="Dosage" onChange={handleChange} required />
        <button type="submit">Save Prescription</button>
      </form>
    </div>
  );
};

export default Prescriptions;
