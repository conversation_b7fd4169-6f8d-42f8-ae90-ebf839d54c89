import React, { useState, useEffect, useCallback } from "react";
import { Container, Row, Col, Form, Button, Card, Alert, Spinner, InputGroup, Badge, ListGroup, Tabs, Tab } from "react-bootstrap";
import { useNavigate, useLocation } from "react-router-dom";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faCalendarAlt, faUserMd, faStethoscope, faClock, faMapMarkerAlt, faFilter, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { useAuth } from "../../context/AuthContext";
import axios from "axios";
import { API_BASE_URL } from "../../assets/utils/constants";
import moment from "moment";
import "./BookAppointment.css";

const BookAppointment = () => {
  // State for doctors and filtering
  const [allDoctors, setAllDoctors] = useState([]);
  const [filteredDoctors, setFilteredDoctors] = useState([]);
  const [specialties, setSpecialties] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  
  // State for appointment booking
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("");
  const [appointmentReason, setAppointmentReason] = useState("");
  
  // UI state
  const [activeTab, setActiveTab] = useState("search");
  const [loading, setLoading] = useState(false);
  const [loadingTimeSlots, setLoadingTimeSlots] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  
  // Hooks
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Parse query parameters (for direct booking from dashboard)
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const doctorId = queryParams.get("doctor");
    
    if (doctorId) {
      // If doctorId is provided in URL, we'll set it once doctors are loaded
      setActiveTab("booking");
    }
  }, [location]);

  // Fetch doctors and specialties on component mount
  useEffect(() => {
    setLoading(true);
    setError("");
    
    // Define a function to fetch doctors
    const fetchDoctors = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/doctors`);
        console.log('Doctors data from API:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching doctors:', error);
        return [];
      }
    };
    
    // Define a function to fetch specialties
    const fetchSpecialties = async () => {
      try {
        // First try the public endpoint
        const response = await axios.get(`${API_BASE_URL}/api/public/specialties`);
        console.log('Specialties data from API:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching specialties:', error);
        // Return empty array as fallback
        return [];
      }
    };
    
    // Execute both fetches in parallel
    Promise.all([
      fetchDoctors(),
      fetchSpecialties()
    ])
    .then(([doctorsData, specialtiesData]) => {
      // Process doctors data
      if (Array.isArray(doctorsData) && doctorsData.length > 0) {
        setAllDoctors(doctorsData);
        setFilteredDoctors(doctorsData);
      } else {
        console.warn('No doctors found or invalid data format');
        setAllDoctors([]);
        setFilteredDoctors([]);
      }
      
      // Process specialties data
      if (Array.isArray(specialtiesData) && specialtiesData.length > 0) {
        setSpecialties(specialtiesData);
      } else {
        console.warn('No specialties found or invalid data format');
        setSpecialties([]);
      }
      
      // If doctor ID was provided in URL, select that doctor
      const queryParams = new URLSearchParams(location.search);
      const doctorId = queryParams.get("doctor");
      
      if (doctorId && Array.isArray(doctorsData)) {
        const doctor = doctorsData.find(doc => doc.id && doc.id.toString() === doctorId);
        if (doctor) {
          setSelectedDoctor(doctor);
          setActiveTab("booking");
        } else {
          console.error(`Doctor with ID ${doctorId} not found in the database`);
          setError(`The selected doctor could not be found. Please try selecting another doctor.`);
        }
      }
    })
    .catch(err => {
      console.error("Error processing data:", err);
      setError("Failed to load doctors and specialties. Please try again later.");
    })
    .finally(() => {
      setLoading(false);
    });
  }, [location.search]);
  
  // Filter doctors based on search term and specialty
  useEffect(() => {
    if (!Array.isArray(allDoctors) || allDoctors.length === 0) {
      return;
    }
    
    let filtered = [...allDoctors];
    
    // Filter by search term
    if (searchTerm && searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doctor => {
        const nameMatch = doctor.name && doctor.name.toLowerCase().includes(term);
        const specialtyMatch = doctor.specialty && doctor.specialty.name && 
                             doctor.specialty.name.toLowerCase().includes(term);
        return nameMatch || specialtyMatch;
      });
    }
    
    // Filter by specialty
    if (selectedSpecialty && selectedSpecialty !== "") {
      filtered = filtered.filter(doctor => {
        // Check if doctor has specialty property and it matches selected specialty
        return doctor.specialty && 
               ((typeof doctor.specialty === 'object' && doctor.specialty.id && 
                 doctor.specialty.id.toString() === selectedSpecialty) ||
                (typeof doctor.specialty === 'object' && doctor.specialty.name && 
                 doctor.specialty.name === selectedSpecialty));
      });
    }
    
    setFilteredDoctors(filtered);
  }, [allDoctors, searchTerm, selectedSpecialty]);
  
  // Fetch available time slots when doctor or date changes
  useEffect(() => {
    if (selectedDoctor && selectedDate) {
      fetchAvailableTimeSlots(selectedDoctor.id, selectedDate);
    }
  }, [selectedDoctor, selectedDate]);

  // Select a doctor and move to booking tab
  const handleSelectDoctor = (doctor) => {
    setSelectedDoctor(doctor);
    setSelectedTimeSlot("");
    setAvailableTimeSlots([]);
    setActiveTab("booking");
    
    // Reset error and success messages
    setError("");
    setSuccess("");
  };

  // Handle date change in calendar
  const handleDateChange = (date) => {
    setSelectedDate(date);
    setSelectedTimeSlot("");
    setAvailableTimeSlots([]);
    
    // Reset error and success messages
    setError("");
    setSuccess("");
  };
  
  // Handle specialty filter change
  const handleSpecialtyChange = (e) => {
    setSelectedSpecialty(e.target.value);
  };
  
  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle time slot selection
  const handleTimeSlotSelect = (time) => {
    setSelectedTimeSlot(time);
  };
  
  // Handle appointment reason input
  const handleReasonChange = (e) => {
    setAppointmentReason(e.target.value);
  };

  // Fetch available time slots for a doctor on a specific date
  const fetchAvailableTimeSlots = useCallback(async (doctorId, date) => {
    if (!doctorId) {
      console.error("Cannot fetch time slots: No doctor ID provided");
      return;
    }
    
    setLoadingTimeSlots(true);
    setError("");
    
    try {
      const formattedDate = moment(date).format("YYYY-MM-DD");
      console.log(`Fetching availability for doctor ID: ${doctorId} on date: ${formattedDate}`);
      
      // First, fetch the doctor's availability schedule
      const availabilityResponse = await axios.get(`${API_BASE_URL}/api/doctors/${doctorId}/availability?date=${formattedDate}`);
      const availabilityData = availabilityResponse.data;
      console.log('Doctor availability data:', availabilityData);
      
      // Next, fetch existing appointments to mark booked slots
      const appointmentsResponse = await axios.get(`${API_BASE_URL}/api/appointments/doctor/${doctorId}?date=${formattedDate}`);
      const appointmentsData = appointmentsResponse.data;
      console.log('Doctor appointments data:', appointmentsData);
      
      // Extract times from booked appointments
      const bookedTimes = appointmentsData.map(appointment => {
        const appointmentTime = new Date(appointment.appointmentDate);
        return `${appointmentTime.getHours()}:00`;
      });
      
      // Generate time slots based on doctor's availability
      let slots = [];
      
      if (Array.isArray(availabilityData) && availabilityData.length > 0) {
        // Process each availability slot for the selected day
        availabilityData.forEach(slot => {
          // Parse start and end times
          const dayOfWeek = new Date(date).getDay();
          
          // Only use slots that match the day of week
          if (slot.dayOfWeek === dayOfWeek) {
            const startParts = slot.startTime.split(':');
            const endParts = slot.endTime.split(':');
            
            const startHour = parseInt(startParts[0]);
            const endHour = parseInt(endParts[0]);
            
            // Generate hourly slots (not half-hourly as requested by user)
            for (let hour = startHour; hour < endHour; hour++) {
              const timeString = `${hour}:00`;
              const displayTime = `${hour > 12 ? hour - 12 : hour}:00 ${hour >= 12 ? 'PM' : 'AM'}`;
              
              // Check if this slot is already booked
              const isBooked = bookedTimes.includes(timeString);
              
              slots.push({
                time: displayTime,
                rawTime: timeString,
                isBooked: isBooked
              });
            }
          }
        });
      } else {
        // Fallback: Use default working hours (9 AM to 5 PM)
        const startHour = 9;
        const endHour = 17;
        
        for (let hour = startHour; hour < endHour; hour++) {
          const timeString = `${hour}:00`;
          const displayTime = `${hour > 12 ? hour - 12 : hour}:00 ${hour >= 12 ? 'PM' : 'AM'}`;
          
          // Check if this slot is already booked
          const isBooked = bookedTimes.includes(timeString);
          
          slots.push({
            time: displayTime,
            rawTime: timeString,
            isBooked: isBooked
          });
        }
      }
      
      console.log('Generated time slots:', slots);
      setAvailableTimeSlots(slots);
      
      if (slots.length === 0) {
        setError("No available time slots found for this doctor on the selected date.");
      }
    } catch (err) {
      console.error("Error fetching time slots:", err);
      setError("Failed to load available time slots. Please try again later.");
      setAvailableTimeSlots([]);
    } finally {
      setLoadingTimeSlots(false);
    }
  }, []);

  // Handle appointment booking form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedDoctor || !selectedDate || !selectedTimeSlot) {
      setError("Please select a doctor, date, and time slot.");
      return;
    }
    
    setLoading(true);
    setError("");
    setSuccess("");
    
    try {
      const appointmentData = {
        doctorId: selectedDoctor.id,
        patientId: user.id,
        date: moment(selectedDate).format("YYYY-MM-DD"),
        time: selectedTimeSlot,
        reason: appointmentReason || "General checkup"
      };
      
      console.log("Booking appointment with data:", appointmentData);
      const response = await axios.post(`${API_BASE_URL}/api/appointments`, appointmentData);
      
      console.log('Appointment booking response:', response);
      
      if (response.status === 200 || response.status === 201) {
        setSuccess("Appointment booked successfully! The doctor will be notified of your request.");
        setAppointmentReason("");
        setSelectedTimeSlot("");
      } else {
        setError("Failed to book appointment. Please try again later.");
      }
    } catch (err) {
      console.error("Error booking appointment:", err);
      setError(`Failed to book appointment: ${err.response?.data || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Go back to doctor selection
  const handleBackToSearch = () => {
    setActiveTab("search");
  };

  return (
    <Container className="py-5 mt-5">
      <Row className="justify-content-center">
        <Col md={10}>
          <Card className="shadow border-0">
            <Card.Body className="p-4">
              <h2 className="text-center mb-4">Book an Appointment</h2>
              
              <Tabs
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k)}
                className="mb-4"
              >
                <Tab eventKey="search" title="Select Doctor">
                  {/* Search and Filter */}
                  <Row className="mb-4">
                    <Col md={8}>
                      <InputGroup>
                        <InputGroup.Text>
                          <FontAwesomeIcon icon={faSearch} />
                        </InputGroup.Text>
                        <Form.Control
                          placeholder="Search doctors by name or specialty..."
                          value={searchTerm}
                          onChange={handleSearchChange}
                        />
                      </InputGroup>
                    </Col>
                    <Col md={4}>
                      <Form.Select 
                        value={selectedSpecialty}
                        onChange={handleSpecialtyChange}
                      >
                        <option value="">All Specialties</option>
                        {specialties.map((specialty, index) => (
                          <option 
                            key={specialty.id || index} 
                            value={specialty.id || specialty.name}
                          >
                            {specialty.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Col>
                  </Row>
                  
                  {/* Error Message */}
                  {error && (
                    <Alert variant="danger" className="mb-4">
                      {error}
                    </Alert>
                  )}
                  
                  {/* Doctors List */}
                  {loading ? (
                    <div className="text-center py-5">
                      <Spinner animation="border" variant="primary" />
                      <p className="mt-3">Loading doctors...</p>
                    </div>
                  ) : filteredDoctors.length === 0 ? (
                    <Alert variant="info">
                      No doctors found matching your criteria. Please try a different search.
                    </Alert>
                  ) : (
                    <Row>
                      {filteredDoctors.map((doctor) => (
                        <Col md={6} key={doctor.id} className="mb-4">
                          <Card className="h-100 shadow-sm hover-card">
                            <Card.Body>
                              <div className="d-flex align-items-center mb-3">
                                <div className="doctor-avatar me-3">
                                  <FontAwesomeIcon icon={faUserMd} size="2x" className="text-primary" />
                                </div>
                                <div>
                                  <h5 className="mb-0">{(doctor.role === 'DOCTOR' || doctor.type === 'DOCTOR') ? `Dr. ${doctor.name}` : doctor.name}</h5>
                                  <p className="text-muted mb-0">
                                    {doctor.specialty?.name || "General Medicine"}
                                  </p>
                                </div>
                              </div>
                              
                              <div className="mb-3">
                                <div className="d-flex align-items-center mb-2">
                                  <FontAwesomeIcon icon={faStethoscope} className="text-primary me-2" />
                                  <span>Experience: {doctor.yearsOfExperience || "5+"} years</span>
                                </div>
                                <div className="d-flex align-items-center">
                                  <FontAwesomeIcon icon={faMapMarkerAlt} className="text-primary me-2" />
                                  <span>{doctor.location || "Main Hospital"}</span>
                                </div>
                              </div>
                              
                              <Button 
                                variant="primary" 
                                className="w-100"
                                onClick={() => handleSelectDoctor(doctor)}
                              >
                                Book Appointment
                                <FontAwesomeIcon icon={faChevronRight} className="ms-2" />
                              </Button>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Tab>
                
                <Tab eventKey="booking" title="Schedule Appointment">
                  {/* Success Message */}
                  {success && (
                    <Alert variant="success" className="mb-4">
                      {success}
                    </Alert>
                  )}
                  
                  {/* Error Message */}
                  {error && (
                    <Alert variant="danger" className="mb-4">
                      {error}
                    </Alert>
                  )}
                  
                  {selectedDoctor ? (
                    <Form onSubmit={handleSubmit}>
                      {/* Doctor Info */}
                      <div className="selected-doctor-info mb-4 p-3 border rounded bg-light">
                        <h5>Selected Doctor</h5>
                        <div className="d-flex align-items-center">
                          <FontAwesomeIcon icon={faUserMd} size="lg" className="text-primary me-3" />
                          <div>
                            <h6 className="mb-0">{(selectedDoctor.role === 'DOCTOR' || selectedDoctor.type === 'DOCTOR') ? `Dr. ${selectedDoctor.name}` : selectedDoctor.name}</h6>
                            <p className="text-muted mb-0">{selectedDoctor.specialty?.name || "General Medicine"}</p>
                          </div>
                        </div>
                      </div>
                      
                      <Row className="mb-4">
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>Reason for Visit</Form.Label>
                            <Form.Control
                              as="textarea"
                              rows={3}
                              placeholder="Please describe your symptoms or reason for the appointment..."
                              value={appointmentReason}
                              onChange={handleReasonChange}
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group>
                            <Form.Label>Select Date</Form.Label>
                            <div className="calendar-container">
                              <Calendar
                                onChange={handleDateChange}
                                value={selectedDate}
                                minDate={new Date()}
                                className="w-100 border rounded p-2"
                              />
                            </div>
                          </Form.Group>
                        </Col>
                      </Row>
                      
                      <Form.Group className="mb-4">
                        <Form.Label>Select Time Slot</Form.Label>
                        {loadingTimeSlots ? (
                          <div className="text-center py-3">
                            <Spinner animation="border" size="sm" /> Loading available times...
                          </div>
                        ) : availableTimeSlots.length === 0 ? (
                          <Alert variant="info">
                            No available time slots for the selected date. Please choose another date.
                          </Alert>
                        ) : (
                          <div className="time-slots-container d-flex flex-wrap gap-2">
                            {availableTimeSlots.map(slot => (
                              <Button
                                key={slot.rawTime}
                                variant={selectedTimeSlot === slot.rawTime ? "primary" : "outline-primary"}
                                onClick={() => handleTimeSlotSelect(slot.rawTime)}
                                className="time-slot-btn"
                                disabled={slot.isBooked}
                              >
                                {slot.time}
                                {slot.isBooked && <span className="ms-2">(Booked)</span>}
                              </Button>
                            ))}
                          </div>
                        )}
                      </Form.Group>
                      
                      <div className="d-flex justify-content-between mt-4">
                        <Button
                          variant="outline-secondary"
                          onClick={handleBackToSearch}
                          disabled={loading}
                        >
                          Back to Doctor Selection
                        </Button>
                        
                        <Button
                          variant="primary"
                          type="submit"
                          disabled={loading || !selectedDoctor || !selectedDate || !selectedTimeSlot}
                          className="px-4"
                        >
                          {loading ? (
                            <>
                              <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                              />
                              Booking Appointment...
                            </>
                          ) : (
                            "Confirm Appointment"
                          )}
                        </Button>
                      </div>
                    </Form>
                  ) : (
                    <Alert variant="warning" className="mt-3">
                      Please select a doctor first.
                      <Button 
                        variant="link" 
                        onClick={() => setActiveTab("search")}
                        className="d-block mx-auto mt-2"
                      >
                        Go to Doctor Selection
                      </Button>
                    </Alert>
                  )}
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default BookAppointment;