import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { resetPassword } from "../../assets/utils/api";
import { Alert, Form, Button } from "react-bootstrap";

const ResetPassword = () => {
  const [params] = useSearchParams();
  const token = params.get("token");
  const [password, setPassword] = useState("");
  const [msg, setMsg] = useState("");
  const [err, setErr] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await resetPassword({ token, password });
      setMsg("Password successfully reset!");
    } catch (error) {
      setErr("Token invalid or expired.");
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      {msg && <Alert variant="success">{msg}</Alert>}
      {err && <Alert variant="danger">{err}</Alert>}
      <Form.Group>
        <Form.Label>New Password</Form.Label>
        <Form.Control
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </Form.Group>
      <Button type="submit" className="mt-3">Reset Password</Button>
    </Form>
  );
};

export default ResetPassword;
