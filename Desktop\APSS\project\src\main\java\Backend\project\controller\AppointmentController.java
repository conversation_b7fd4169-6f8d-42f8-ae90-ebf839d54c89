package Backend.project.controller;

import Backend.project.dtos.AppointmentDTO;
import Backend.project.model.AppointmentStatus;
import Backend.project.model.Patient;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import Backend.project.model.Appointment;
import Backend.project.model.Doctor;
import Backend.project.services.AppointmentService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/appointments")
@CrossOrigin(origins = "http://localhost:3000")
public class AppointmentController {

    private final AppointmentService appointmentService;

    public AppointmentController(AppointmentService appointmentService) {
        this.appointmentService = appointmentService;
    }
    
    @GetMapping("/doctor/{doctorId}/today")
    public List<AppointmentDTO> getTodayAppointments(@PathVariable Long doctorId) {
        LocalDate today = LocalDate.now();
        return appointmentService.findByDoctorIdAndDate(doctorId, today);
    }
    
    @PostMapping
    public ResponseEntity<?> createAppointment(@RequestBody Map<String, Object> requestData) {
        try {
            // Extract data from the request
            Long doctorId = Long.valueOf(requestData.get("doctorId").toString());
            Long patientId = Long.valueOf(requestData.get("patientId").toString());
            String dateStr = (String) requestData.get("date");
            String timeStr = (String) requestData.get("time");
            String reason = (String) requestData.get("reason");
            
            // Create doctor and patient objects
            Doctor doctor = new Doctor();
            doctor.setId(doctorId);
            
            Patient patient = new Patient();
            patient.setId(patientId);
            
            // Parse date and time
            LocalDate date = LocalDate.parse(dateStr);
            LocalTime time = LocalTime.parse(timeStr);
            LocalDateTime appointmentDateTime = LocalDateTime.of(date, time);
            
            // Create appointment object
            Appointment appointment = new Appointment();
            appointment.setDoctor(doctor);
            appointment.setPatient(patient);
            appointment.setAppointmentDate(appointmentDateTime);
            appointment.setReason(reason);
            appointment.setStatus(AppointmentStatus.PENDING);
            
            // Set duration (30 minutes)
            LocalTime duration = LocalTime.of(0, 30);
            appointment.setDuration(duration);
            
            // Save the appointment
            Appointment savedAppointment = appointmentService.createAppointment(appointment);
            return ResponseEntity.ok(savedAppointment);
        } catch (Exception e) {
            System.err.println("Error creating appointment: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error creating appointment: " + e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<?> modifyAppointment(@PathVariable Long id, @RequestBody Appointment updateAppointment) {
        try {
            updateAppointment.setId(id); // Set the ID from the path variable
            Appointment updated = appointmentService.modifyAppointment(updateAppointment);
            return ResponseEntity.ok(updated);
        } catch (Exception e) {
            System.err.println("Error updating appointment: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error updating appointment: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getAppointmentById(@PathVariable Long id) {
        try {
            Optional<Appointment> appointment = appointmentService.getAppointmentById(id);
            if (appointment.isPresent()) {
                return ResponseEntity.ok(appointment.get());
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Appointment not found with ID: " + id);
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving appointment: " + e.getMessage());
        }
    }

    @GetMapping("/patient/{patientId}")
    public ResponseEntity<?> getAppointmentsByPatient(@PathVariable Long patientId) {
        try {
            Patient patient = new Patient();
            patient.setId(patientId);
            List<Appointment> appointments = appointmentService.getAppointmentsByPatient(patient);
            return ResponseEntity.ok(appointments);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving patient appointments: " + e.getMessage());
        }
    }
    
    @GetMapping("/patient/{patientId}/upcoming")
    public ResponseEntity<?> getUpcomingAppointmentsByPatient(@PathVariable Long patientId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<Appointment> appointments = appointmentService.findByPatientIdAndAppointmentDateAfterOrderByAppointmentDate(patientId, now);
            return ResponseEntity.ok(appointments);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving upcoming patient appointments: " + e.getMessage());
        }
    }

    @GetMapping("/doctor/{doctorId}")
    public ResponseEntity<?> getAppointmentsByDoctor(@PathVariable Long doctorId) {
        try {
            Doctor doctor = new Doctor();
            doctor.setId(doctorId);
            List<Appointment> appointments = appointmentService.getAppointmentsByDoctor(doctor);
            return ResponseEntity.ok(appointments);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving doctor appointments: " + e.getMessage());
        }
    }
    
    @GetMapping("/doctor/{doctorId}/upcoming")
    public ResponseEntity<?> getUpcomingAppointmentsByDoctor(@PathVariable Long doctorId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<Appointment> appointments = appointmentService.findByDoctorIdAndAppointmentDateAfterOrderByAppointmentDate(doctorId, now);
            return ResponseEntity.ok(appointments);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error retrieving upcoming doctor appointments: " + e.getMessage());
        }
    }
    
    @PutMapping("/{appointmentId}/status")
    public ResponseEntity<?> updateAppointmentStatus(@PathVariable Long appointmentId, 
                                                   @RequestBody Map<String, String> requestData) {
        try {
            String statusStr = requestData.get("status");
            AppointmentStatus status = AppointmentStatus.valueOf(statusStr);
            
            Appointment updatedAppointment = appointmentService.updateAppointmentStatus(appointmentId, status);
            return ResponseEntity.ok(updatedAppointment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body("Error updating appointment status: " + e.getMessage());
        }
    }
    
    @PostMapping("/{appointmentId}/confirm")
    public ResponseEntity<?> confirmAppointment(@PathVariable Long appointmentId) {
        try {
            Appointment confirmedAppointment = appointmentService.updateAppointmentStatus(appointmentId, AppointmentStatus.CONFIRMED);
            return ResponseEntity.ok(confirmedAppointment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body("Error confirming appointment: " + e.getMessage());
        }
    }
    
    @PostMapping("/{appointmentId}/decline")
    public ResponseEntity<?> declineAppointment(@PathVariable Long appointmentId) {
        try {
            Appointment declinedAppointment = appointmentService.updateAppointmentStatus(appointmentId, AppointmentStatus.CANCELED);
            return ResponseEntity.ok(declinedAppointment);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body("Error declining appointment: " + e.getMessage());
        }
    }
    


    @DeleteMapping("/{id}")
    public ResponseEntity<?> cancelAppointment(@PathVariable Long id) {
        try {
            appointmentService.cancelAppointment(id);
            return ResponseEntity.ok("Appointment canceled successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error canceling appointment: " + e.getMessage());
        }
    }
}