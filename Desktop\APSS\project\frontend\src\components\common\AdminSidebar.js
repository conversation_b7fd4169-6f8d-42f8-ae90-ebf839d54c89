import React from "react";
import { Link, useLocation } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faHome, 
  faUser, 
  faUsers, 
  faCalendarAlt, 
  faCreditCard, 
  faChartLine, 
  faCog, 
  faBell, 
  faComments,
  faSignOutAlt
} from "@fortawesome/free-solid-svg-icons";
import "./Sidebar.css";

const AdminSidebar = ({ onLogout }) => {
  const location = useLocation();
  const user = JSON.parse(localStorage.getItem("user"));

  const menuItems = [
    {
      path: "/admin/dashboard",
      icon: faHome,
      label: "Dashboard"
    },
    {
      path: "/admin/profile",
      icon: faUser,
      label: "Profile"
    },
    {
      path: "/admin/users",
      icon: faUsers,
      label: "Manage Users"
    },
    {
      path: "/admin/doctors",
      icon: faUsers,
      label: "Manage Doctors"
    },
    {
      path: "/admin/appointments",
      icon: faCalendarAlt,
      label: "Manage Appointments"
    },
    {
      path: "/admin/payments",
      icon: faCreditCard,
      label: "Manage Payments"
    },
    {
      path: "/admin/reports",
      icon: faChartLine,
      label: "Reports"
    },
    {
      path: "/admin/feedback",
      icon: faComments,
      label: "Feedback Management"
    },
    {
      path: "/admin/notifications",
      icon: faBell,
      label: "Notifications"
    },
    {
      path: "/admin/settings",
      icon: faCog,
      label: "System Settings"
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Admin Portal</h3>
        <p className="user-info">Admin: {user?.name || 'Administrator'}</p>
      </div>
      
      <nav className="sidebar-nav">
        <ul>
          {menuItems.map((item) => (
            <li key={item.path}>
              <Link 
                to={item.path} 
                className={location.pathname === item.path ? 'active' : ''}
              >
                <FontAwesomeIcon icon={item.icon} className="me-2" />
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className="sidebar-footer">
        <button onClick={onLogout} className="logout-btn">
          <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default AdminSidebar; 