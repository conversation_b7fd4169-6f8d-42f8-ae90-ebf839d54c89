package Backend.project.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name= "Notification")
public class Notification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Patient patient;

    private String message;
    private LocalDateTime DateTimeSent;
    private boolean read = false;

    // Getters et Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Patient getPatient() {
        return patient;
    }

    public void setPatient(Patient patient) {
        this.patient = patient;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getDateTimeSent() {
        return DateTimeSent;
    }

    public void setDateTimeSent(LocalDateTime dateTimeSent) {
        DateTimeSent = dateTimeSent;
    }
    
    public boolean isRead() {
        return read;
    }
    
    public void setRead(boolean read) {
        this.read = read;
    }
}

