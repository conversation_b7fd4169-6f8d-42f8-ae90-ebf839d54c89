import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, <PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON>, Tab } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarAlt, faPhone, faEnvelope, faMapMarkerAlt, faStar, faStethoscope } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';
import { API_BASE_URL } from '../../assets/utils/constants';

// Import local doctor images - same as DoctorList.js
import doctor1 from '../../assets/images/41808433_l.jpg';
import doctor2 from '../../assets/images/What-is-the-Role-of-a-Primary-Care-Physician.jpg';
import doctor3 from '../../assets/images/young-asian-female-dentist-white-coat-posing-clinic-equipment.jpg';
import doctor4 from '../../assets/images/about-doctor-speaking-with-medical-team.jpg';
import doctor5 from '../../assets/images/female-doctor-with-presenting-hand-gesture.jpg';
import doctor6 from '../../assets/images/portrait-successful-mid-adult-doctor-with-crossed-arms.jpg';

// Array of doctor images - same order as DoctorList.js
const doctorImages = [doctor1, doctor2, doctor3, doctor4, doctor5, doctor6];

const DoctorDetail = () => {
  const { doctorId } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  
  const [doctor, setDoctor] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
 useEffect(() => {
    const fetchDoctorDetails = async () => {
      setLoading(true);
      setError('');
      try {
        // Use the API utility function to get doctor details
        const response = await axios.get(`${API_BASE_URL}/api/doctors/${doctorId}`);
        console.log('Doctor data:', response.data);
        setDoctor(response.data);
      } catch (err) {
        console.error('Error fetching doctor details:', err);
        setError('Failed to load doctor details. Please try again later.');
        
        // Only use mock data in development environment as fallback
        if (process.env.NODE_ENV === 'development') {
          console.log('Using mock data as fallback');
          const mockDoctors = [
          {
            id: "1",
            firstName: "John",
            lastName: "Smith",
            specialty: { name: "Cardiology" },
            bio: "Board-certified cardiologist with over 10 years of experience in diagnosing and treating heart conditions.",
            phone: "************",
            email: "<EMAIL>",
            address: "123 Medical Center Dr, Suite 101",
            education: [
              "MD, Harvard Medical School",
              "Residency, Johns Hopkins Hospital",
              "Fellowship in Cardiology, Mayo Clinic"
            ],
            experience: "10+ years",
            rating: 4.8,
            reviewCount: 124,
            availableDays: ["Monday", "Tuesday", "Thursday", "Friday"],
            availableHours: "9:00 AM - 5:00 PM"
          },
          {
            id: "2",
            firstName: "Sarah",
            lastName: "Jonhson",
            specialty: { name: "Neurology" },
            bio: "Specializing in neurological disorders with a focus on stroke prevention and treatment.",
            phone: "************",
            email: "<EMAIL>",
            address: "456 Health Parkway, Suite 202",
            education: [
              "MD, Stanford University School of Medicine",
              "Residency, Massachusetts General Hospital",
              "Fellowship in Neurology, Cleveland Clinic"
            ],
            experience: "8+ years",
            rating: 4.7,
            reviewCount: 98,
            availableDays: ["Monday", "Wednesday", "Friday"],
            availableHours: "8:00 AM - 4:00 PM"
          },
          {
            id: "3",
            firstName: "Michael",
            lastName: "Brown",
            specialty: { name: "Pediatrics" },
            bio: "Dedicated pediatrician with a passion for child development and preventive care.",
            phone: "************",
            email: "<EMAIL>",
            address: "789 Children's Way, Suite 303",
            education: [
              "MD, University of California, San Francisco",
              "Residency, Children's Hospital of Philadelphia",
              "Fellowship in Pediatric Care, Boston Children's Hospital"
            ],
            experience: "12+ years",
            rating: 4.9,
            reviewCount: 156,
            availableDays: ["Tuesday", "Wednesday", "Thursday", "Friday"],
            availableHours: "9:00 AM - 5:00 PM"
          },
          {
            id: "4",
            firstName: "Emily",
            lastName: "Davis",
            specialty: { name: "Orthopedics" },
            bio: "Orthopedic surgeon specializing in sports medicine and joint replacement.",
            phone: "************",
            email: "<EMAIL>",
            address: "321 Sports Medicine Blvd, Suite 404",
            education: [
              "MD, Duke University School of Medicine",
              "Residency, Hospital for Special Surgery",
              "Fellowship in Sports Medicine, Andrews Institute"
            ],
            experience: "15+ years",
            rating: 4.6,
            reviewCount: 187,
            availableDays: ["Monday", "Tuesday", "Thursday"],
            availableHours: "7:00 AM - 3:00 PM"
          },
          {
            id: "5",
            firstName: "Robert",
            lastName: "Wilson",
            specialty: { name: "Psychiatry" },
            bio: "Compassionate psychiatrist specializing in mood disorders, anxiety, and PTSD treatment.",
            phone: "************",
            email: "<EMAIL>",
            address: "678 Mental Health Blvd, Suite 606",
            education: [
              "MD, Johns Hopkins University School of Medicine",
              "Residency, Massachusetts General Hospital",
              "Fellowship in Neuropsychiatry, UCLA Medical Center"
            ],
            experience: "11+ years",
            rating: 4.7,
            reviewCount: 163,
            availableDays: ["Tuesday", "Thursday", "Friday"],
            availableHours: "10:00 AM - 6:00 PM"
          }, {
            id: "6",
            firstName: "Jennifer",
            lastName: "Lee",
            specialty: { name: "Dermatology" },
            bio: "Expert dermatologist with a focus on skin cancer prevention and treatment of complex skin conditions.",
            phone: "************",
            email: "<EMAIL>",
            address: "567 Wellness Ave, Suite 505",
            education: [
              "MD, Yale School of Medicine",
              "Residency, NYU Langone Medical Center",
              "Fellowship in Dermatologic Surgery, Mayo Clinic"
            ],
            experience: "9+ years",
            rating: 4.9,
            reviewCount: 142,
            availableDays: ["Monday", "Wednesday", "Friday"],
            availableHours: "9:00 AM - 4:00 PM"
          }
        ];
        
        // Find the doctor with the matching ID or use the first one as default
          const foundDoctor = mockDoctors.find(d => d.id === doctorId) || mockDoctors[0];
          setDoctor(foundDoctor);
        }
      } finally {
        setLoading(false);
      }
    };
    
    fetchDoctorDetails();
  }, [doctorId]);
  
  const handleBookAppointment = () => {
    try {
      // Check if user is authenticated
      if (!isAuthenticated) {
        navigate('/login', { state: { from: `/doctors/${doctorId}`, message: 'Please log in to book an appointment' } });
        return;
      }
      
      // If user is a patient, redirect to booking page
      if (user?.role === 'PATIENT' || user?.role === 'patient') {
        navigate(`/patient/book-appointment?doctor=${doctorId}`);
      } else {
        // If user is not a patient (e.g., doctor or admin)
        alert('Only patients can book appointments. Please log in with a patient account.');
      }
    } catch (error) {
      console.error('Error navigating to booking page:', error);
    }
  };
  
  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status" variant="primary">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading doctor profile...</p>
      </Container>
    );
  }
  
  if (!doctor && error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          {error || "Doctor not found. Please try again later."}
        </Alert>
      </Container>
    );
  }
  
  // Select a doctor image based on the doctor ID
  const doctorImageIndex = parseInt(doctorId) % doctorImages.length;
  const doctorImage = doctorImages[doctorImageIndex];
  
  return (
    <Container className="py-5">
      {error && <Alert variant="info" className="mb-4">{error}</Alert>}
      
      <Row className="mb-5">
        <Col lg={4} className="mb-4 mb-lg-0">
          <Card className="border-0 shadow-sm">
            <Card.Img 
              variant="top" 
              src={doctorImage} 
              alt={`Dr. ${doctor.firstName} ${doctor.lastName}`}
              style={{ height: '300px', objectFit: 'cover' }}
            />
            <Card.Body className="text-center p-4">
              <h2 className="mb-1">{(doctor.role === 'DOCTOR' || doctor.type === 'DOCTOR') ? `Dr. ${doctor.firstName} ${doctor.lastName}` : `${doctor.firstName} ${doctor.lastName}`}</h2>
              <p className="text-primary mb-3">{doctor.specialty?.name || "General Medicine"}</p>
              
              <div className="d-flex justify-content-center mb-3">
                {[...Array(5)].map((_, i) => (
                  <FontAwesomeIcon 
                    key={i} 
                    icon={faStar} 
                    className={i < Math.floor(doctor.rating || 4) ? "text-warning" : "text-muted"}
                  />
                ))}
                <span className="ms-2">
                  {doctor.rating || 4.5} ({doctor.reviewCount || 100}+ reviews)
                </span>
              </div>
              
              <div className="d-grid gap-2">
                <Button 
                  variant="primary" 
                  size="lg" 
                  onClick={handleBookAppointment}
                  className="mb-2"
                >
                  <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                  Book Appointment
                </Button>
              </div>
            </Card.Body>
          </Card>
          
          <Card className="border-0 shadow-sm mt-4">
            <Card.Body>
              <h5 className="mb-3">Contact Information</h5>
              <ul className="list-unstyled">
                <li className="mb-3">
                  <FontAwesomeIcon icon={faPhone} className="me-2 text-primary" />
                  {doctor.phone || "************"}
                </li>
                <li className="mb-3">
                  <FontAwesomeIcon icon={faEnvelope} className="me-2 text-primary" />
                  {doctor.email || "<EMAIL>"}
                </li>
                <li className="mb-3">
                  <FontAwesomeIcon icon={faMapMarkerAlt} className="me-2 text-primary" />
                  {doctor.address || "123 Medical Center Dr, Suite 101"}
                </li>
              </ul>
            </Card.Body>
          </Card>
          
          <Card className="border-0 shadow-sm mt-4">
            <Card.Body>
              <h5 className="mb-3">Available Hours</h5>
              <p className="mb-2"><strong>Days:</strong> {doctor.availableDays?.join(", ") || "Monday - Friday"}</p>
              <p><strong>Hours:</strong> {doctor.availableHours || "9:00 AM - 5:00 PM"}</p>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={8}>
          <Card className="border-0 shadow-sm mb-4">
            <Card.Body className="p-4">
              <h3 className="mb-4">About {(doctor.role === 'DOCTOR' || doctor.type === 'DOCTOR') ? `Dr. ${doctor.firstName} ${doctor.lastName}` : `${doctor.firstName} ${doctor.lastName}`}</h3>
              <p>{doctor.bio || "Experienced healthcare professional dedicated to providing quality patient care."}</p>
            </Card.Body>
          </Card>
          
          <Tabs defaultActiveKey="education" className="mb-4">
            <Tab eventKey="education" title="Education & Training">
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-4">
                  <h5 className="mb-3">Education</h5>
                  <ul>
                    {(doctor.education || ["MD, Medical University", "Residency, General Hospital", "Fellowship, Specialty Institute"]).map((edu, index) => (
                      <li key={index} className="mb-2">{edu}</li>
                    ))}
                  </ul>
                  
                  <h5 className="mb-3 mt-4">Experience</h5>
                  <p>{doctor.experience || "10+ years of clinical experience"}</p>
                </Card.Body>
              </Card>
            </Tab>
            <Tab eventKey="specialties" title="Specialties">
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-4">
                  <h5 className="mb-3">Areas of Expertise</h5>
                  <div className="d-flex flex-wrap gap-2 mb-4">
                    <Badge bg="primary" className="py-2 px-3">
                      <FontAwesomeIcon icon={faStethoscope} className="me-2" />
                      {doctor.specialty?.name || "General Medicine"}
                    </Badge>
                    <Badge bg="primary" className="py-2 px-3">Preventive Care</Badge>
                    <Badge bg="primary" className="py-2 px-3">Diagnosis</Badge>
                    <Badge bg="primary" className="py-2 px-3">Treatment Planning</Badge>
                  </div>
                </Card.Body>
              </Card>
            </Tab>
          </Tabs>
        </Col>
      </Row>
    </Container>
  );
};

export default DoctorDetail;
