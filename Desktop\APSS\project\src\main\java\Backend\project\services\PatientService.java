package Backend.project.services;

import Backend.project.model.Patient;
import Backend.project.repositories.PatientRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class PatientService {

    private final PatientRepository patientRepository;

    public PatientService(PatientRepository userRepository) {
        this.patientRepository = userRepository;
    }

    public Patient createUser(Patient user) {
        return patientRepository.save(user);
    }

    public Optional<Patient> getUserById(Long id) {
        return patientRepository.findById(id);
    }

    public Optional<Patient> getUserByEmail(String email) {
        return patientRepository.findByEmail(email);
    }

    public List<Patient> getAllUsers() {
        return patientRepository.findAll();
    }

    public void deleteUser(Long id) {
        patientRepository.deleteById(id);
    }
    public List<Patient> getPatients() {
        return patientRepository.findAll();
    }
    
    public Patient updatePatient(Patient patient) {
        return patientRepository.save(patient);
    }
}

