.admin-dashboard {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.welcome-heading {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.admin-tabs .nav-link {
  color: #495057;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: 0.25rem;
  margin-right: 0.5rem;
}

.admin-tabs .nav-link.active {
  color: #0d6efd;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.dashboard-card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
  padding: 1rem 1.25rem;
}

.stat-icon {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto;
  color: white;
}

.bg-primary {
  background-color: #0d6efd;
}

.bg-success {
  background-color: #198754;
}

.bg-info {
  background-color: #0dcaf0;
}

.bg-warning {
  background-color: #ffc107;
}

.search-input {
  border-radius: 2rem;
  padding-left: 1rem;
  border: 1px solid #ced4da;
}

.search-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  border-color: #86b7fe;
}

table th {
  font-weight: 600;
  color: #495057;
}

table td {
  vertical-align: middle;
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  font-weight: 600;
  color: #212529;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .stat-icon {
    width: 3rem;
    height: 3rem;
  }
}
